# CPA DASHBOARD - <PERSON>N<PERSON><PERSON>NMENT CONFIGURATION TEMPLATE
# Copy this file to .env and update with your actual values

# =====
# ENVIRONMENT CONFIGURATION
# =====
NODE_ENV=development
PORT=3003
SERVER_URL=http://localhost:3003

# =====
# FRONTEND CONFIGURATION
# =====
FRONTEND_URL=http://localhost:3000

# =====
# DATABASE CONFIGURATION (PostgreSQL)
# =====
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=cpa_dashboard
DATABASE_USER=postgres
DATABASE_PASS=your_password
DATABASE_DIALECT=postgres
DATABASE_SSL=false

# =====
# JWT CONFIGURATION
# =====
JWT_SECRET=your-jwt-secret-here-minimum-32-characters
JWT_ACCESS_SECRET=your-jwt-access-secret-here
JWT_REFRESH_SECRET=your-jwt-refresh-secret-here
JWT_RESET_SECRET=your-jwt-reset-secret-here
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_RESET_EXPIRY=1h

# =====
# SESSION CONFIGURATION
# =====
SESSION_SECRET=your-session-secret-here-minimum-32-characters

# =====
# QUICKBOOKS INTEGRATION CONFIGURATION
# =====
QUICKBOOKS_CLIENT_ID=your-quickbooks-client-id
QUICKBOOKS_CLIENT_SECRET=your-quickbooks-client-secret
QUICKBOOKS_TOKEN_URL=https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
SANDBOX_URL=https://sandbox-quickbooks.api.intuit.com
REDIRECT_URI=http://localhost:3000/apps/quickbooks/callbacks
ENCRYPTION_KEY=your-32-character-encryption-key-here

# =====
# SQL SERVER CONFIGURATION (for QuickBooks client databases)
# =====
DB_SERVER=your-sql-server-host
DB_USER=your-sql-username
DB_PASSWORD=your-sql-password

# =====
# EMAIL CONFIGURATION
# =====
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
APP_EMAIL_PASSWORD=your-app-password

# =====
# LOGGING CONFIGURATION
# =====
LOG_LEVEL=info
