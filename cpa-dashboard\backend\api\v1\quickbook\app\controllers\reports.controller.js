import { successResponse, errorResponse } from '../utils/response.util.js';
import {
  QUICKBOOKS_REPORTS_LOGS,
  QUICKBOOKS_VALIDATION_LOGS,
} from '../utils/constants/log.constants.js';


import {
  QUICKBOOKS_REPORTS_MESSAGES
} from '../utils/constants/error.constants.js';
import { ERROR_MESSAGES } from '../utils/constants/error.constants.js';
import { HARDCODED_STRINGS } from '../utils/constants/strings.constants.js';
import * as status from '../utils/status_code.utils.js';
import logger from '../../config/logger.config.js';
import { getLastMonthDateRange } from '../utils/date.utils.js';
import reportsService from '../services/reports.service.js';

// Error mapping for HTTP status codes
const ERROR_STATUS_MAP = new Map([
  [status.STATUS_CODE_UNAUTHORIZED, QUICKBOOKS_REPORTS_MESSAGES.AUTH.TOKEN_EXPIRED],
  [status.STATUS_CODE_FORBIDDEN, QUICKBOOKS_REPORTS_MESSAGES.AUTH.ACCESS_DENIED],
  [status.STATUS_CODE_NOT_FOUND, QUICKBOOKS_REPORTS_MESSAGES.REPORTS.COMPANY_NOT_FOUND]
]);

// Report configuration map
const REPORT_CONFIG = {
  TrialBalance: {
    mapFunction: reportsService.mapTrialBalanceToOptimizedStructure,
    saveFunction: reportsService.saveTrialBalanceData,
    successMessage: QUICKBOOKS_REPORTS_MESSAGES.REPORTS.TRIAL_BALANCE_SUCCESS
  },
  ProfitAndLoss: {
    mapFunction: reportsService.mapProfitLossToOptimizedStructure,
    saveFunction: reportsService.saveProfitLossData,
    successMessage: QUICKBOOKS_REPORTS_MESSAGES.REPORTS.PROFIT_LOSS_SUCCESS
  },
  BalanceSheet: {
    mapFunction: reportsService.mapBalanceSheetToOptimizedStructure,
    saveFunction: reportsService.saveBalanceSheetData,
    successMessage: QUICKBOOKS_REPORTS_MESSAGES.REPORTS.BALANCE_SHEET_SUCCESS
  }
};


const validateRequiredFields = (req, res, reportType, requireDates = true) => {
  try {
    const { realmId, quickBookAccesstoken, startDate, endDate } = req.body;
    
    // Use guard clauses for early returns
    if (!realmId) {
      logger.warn(QUICKBOOKS_VALIDATION_LOGS.VALIDATION_FAILED(HARDCODED_STRINGS.VALIDATION_FIELDS.REALM_ID, reportType), { reportType });
      res.status(status.STATUS_CODE_BAD_REQUEST)
         .json(errorResponse(ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD.replace('{field}', HARDCODED_STRINGS.VALIDATION_FIELDS.REALM_ID)));
      return false;
    }

    if (!quickBookAccesstoken) {
      logger.warn(QUICKBOOKS_VALIDATION_LOGS.VALIDATION_FAILED(HARDCODED_STRINGS.VALIDATION_FIELDS.ACCESS_TOKEN, reportType), { reportType });
      res.status(status.STATUS_CODE_BAD_REQUEST)
         .json(errorResponse(ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD.replace('{field}', HARDCODED_STRINGS.VALIDATION_FIELDS.QUICKBOOK_ACCESS_TOKEN)));
      return false;
    }

    if (requireDates && (!startDate || !endDate)) {
      logger.warn(QUICKBOOKS_VALIDATION_LOGS.VALIDATION_FAILED(HARDCODED_STRINGS.VALIDATION_FIELDS.DATE_RANGE, reportType), { reportType });
      res.status(status.STATUS_CODE_BAD_REQUEST)
         .json(errorResponse(ERROR_MESSAGES.VALIDATION.REQUIRED_FIELDS.replace('{fields}', 'startDate, endDate')));
      return false;
    }

    return true;
  } catch (error) {
    logger.error(QUICKBOOKS_VALIDATION_LOGS.VALIDATE_REQUIRED_FIELDS_ERROR, {
      error: error.message,
      stack: error.stack,
      reportType: reportType,
      requireDates: requireDates
    });
    res.status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
       .json(errorResponse(ERROR_MESSAGES.INTERNAL_SERVER_ERROR));
    return false;
  }
};

const handleApiError = (error, res, reportType, realmId) => {
  try {
    logger.error(QUICKBOOKS_REPORTS_LOGS.API_REQUEST_FAILED(reportType, error.message, error.response?.status), { 
      reportType, 
      error: error.message,
      realmId,
      status: error.response?.status
    });

    const errorMessage = ERROR_STATUS_MAP.get(error.response?.status) || 
      ERROR_MESSAGES.GENERIC.PROCESSING_FAILED.replace('{operation}', `${reportType} report processing`);

    const statusCode = error.response?.status || status.STATUS_CODE_INTERNAL_SERVER_ERROR;
    
    return res.status(statusCode).json(errorResponse(errorMessage));
  } catch (err) {
    logger.error(QUICKBOOKS_VALIDATION_LOGS.HANDLE_API_ERROR_ERROR, {
      error: err.message,
      stack: err.stack,
      originalError: error,
      reportType: reportType,
      realmId: realmId
    });
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
               .json(errorResponse(ERROR_MESSAGES.INTERNAL_SERVER_ERROR));
  }
};

const processReportData = async (reportData, mappedData, reportType, realmId) => {
  try {
    // Validate response using guard clause
    if (!reportData?.QueryResponse) {
      logger.warn(QUICKBOOKS_REPORTS_LOGS.NO_DATA_FOUND(reportType, realmId), { reportType, realmId });
      return { error: QUICKBOOKS_REPORTS_MESSAGES.REPORTS.NO_DATA_FOUND.replace('{reportType}', reportType) };
    }

    // Validate mapped data using guard clause
    if (!mappedData?.reportData) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED(reportType), { reportType });
      return { error: ERROR_MESSAGES.PROCESSING.DATA_MAPPING_FAILED };
    }

    return { success: true, data: mappedData };
  } catch (error) {
    logger.error(QUICKBOOKS_VALIDATION_LOGS.PROCESS_REPORT_DATA_ERROR, {
      error: error.message,
      stack: error.stack,
      reportType: reportType,
      realmId: realmId
    });
    return { error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
  }
};

const processReport = async (req, res, reportType) => {
  const config = REPORT_CONFIG[reportType];
  const { quickBookAccesstoken, realmId } = req.body;
  let { startDate, endDate } = req.body;

  logger.info(QUICKBOOKS_REPORTS_LOGS.PROCESSING_START, { reportType, startDate, endDate, realmId });

  try {
    // Early validation return
    if (!validateRequiredFields(req, res, reportType, reportType !== HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS)) {
      return; // Response already sent
    }

    // Auto-generate dates for P&L if missing
    if (reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && (!startDate || !endDate)) {
      const lastMonthRange = getLastMonthDateRange();
      startDate = startDate || lastMonthRange.startDate;
      endDate = endDate || lastMonthRange.endDate;
      
      logger.info(QUICKBOOKS_REPORTS_LOGS.DATES_VALIDATED, { 
        autoStartDate: startDate, autoEndDate: endDate 
      });
    }

    // Create account object
    const mockQuickbookAccount = { realm_id: realmId, access_token: quickBookAccesstoken };
    
    // Fetch report data
    const reportData = await reportsService.getReportDataFromQuickBooks(
      mockQuickbookAccount, startDate, endDate, reportType, quickBookAccesstoken
    );
    
    // Map data
    const mappedData = config.mapFunction(reportData.QueryResponse, null, realmId, startDate, endDate);
    
    // Process and validate
    const processResult = await processReportData(reportData, mappedData, reportType, realmId);
    if (processResult.error) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(errorResponse(processResult.error));
    }
    
    // Save data
    const savedData = await config.saveFunction(mappedData);
    
    // Guard clause for save validation
    if (!savedData?.reportId) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.SAVE_FAILED, { reportType });
      return res.status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
                .json(errorResponse(ERROR_MESSAGES.DATABASE.SAVE_FAILED));
    }

    logger.info(QUICKBOOKS_REPORTS_LOGS.PROCESSING_SUCCESS, { 
      reportType, reportId: savedData.reportId 
    });
    
    // Success response
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(config.successMessage, {
        reportId: savedData.reportId,
        columnsCount: savedData.columnsCount,
        rowsCount: savedData.rowsCount,
        reportType,
        dateRange: { startDate, endDate },
        realmId,
        ...(reportType === HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS && { autoGeneratedDates: !req.body.startDate || !req.body.endDate })
      })
    );

  } catch (error) {
    return handleApiError(error, res, reportType, realmId);
  }
};

/**
 * Streamlined report controllers using configuration
 */
export const fetchTrialBalance = (req, res) => processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE);
export const fetchProfitLoss = (req, res) => processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS);
export const fetchBalanceSheet = (req, res) => processReport(req, res, HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET);



/**
 * Generic report retrieval function
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} reportType - Type of report
 * @param {Function} serviceFunction - Service function to call
 * @returns {Promise<Object>} HTTP response
 */

const getReportsByRealmId = async (req, res, reportType, serviceFunction) => {
  try {
    const { realmId } = req.params;

    // Guard clause for validation
    if (!realmId) {
      return res.status(status.STATUS_CODE_BAD_REQUEST)
                .json(errorResponse(ERROR_MESSAGES.VALIDATION.REQUIRED_FIELD.replace('{field}', HARDCODED_STRINGS.VALIDATION_FIELDS.REALM_ID)));
    }





    const reports = await serviceFunction(realmId, {
      order: HARDCODED_STRINGS.DB_ORDER.CREATED_AT_DESC,
      limit: parseInt(req.query.limit) || 10,
      offset: parseInt(req.query.offset) || 0
    });

    return res.status(status.STATUS_CODE_SUCCESS)
              .json(successResponse(`${reportType} reports retrieved successfully`, {
                reports, realmId, count: reports.length
              }));

  } catch (error) {
    logger.error(QUICKBOOKS_VALIDATION_LOGS.GET_REPORTS_BY_REALM_ID_ERROR, {
      error: error.message,
      stack: error.stack,
      reportType: reportType,
      realmId: req.params?.realmId
    });

    return res.status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
              .json(errorResponse(ERROR_MESSAGES.DATABASE.RETRIEVAL_FAILED));
  }
};

/**
 * Report retrieval controllers using generic function
 */
export const getTrialBalanceReports = (req, res) => 
  getReportsByRealmId(req, res, HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE_DISPLAY, reportsService.getTrialBalanceReportsByRealmId);

export const getProfitLossReports = (req, res) => 
  getReportsByRealmId(req, res, HARDCODED_STRINGS.REPORT_TYPES.PROFIT_LOSS_DISPLAY, reportsService.getProfitLossReportsByRealmId);

export const getBalanceSheetReports = (req, res) => 
  getReportsByRealmId(req, res, HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET_DISPLAY, reportsService.getBalanceSheetReportsByRealmId);