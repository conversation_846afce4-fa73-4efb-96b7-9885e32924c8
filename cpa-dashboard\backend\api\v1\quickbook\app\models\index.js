import sequelize from "../../config/postgres.config.js";
import GLAccountMasterModel from "../../../../../models/gl_account_master.model.js";
import QuickbookAccountModel from "../../../../../models/quickbook_account.model.js";
import TrialBalanceModels from "../../../../../models/trial_balance.model.js";
import ProfitLossModels from "../../../../../models/profit_loss.model.js";
import BalanceSheetModels from "../../../../../models/balance_sheet.model.js";

const models = {
  gl_account_master: GLAccountMasterModel(sequelize),
  quickbook_account: QuickbookAccountModel(sequelize),
  ...TrialBalanceModels(sequelize),
  ...ProfitLossModels(sequelize),
  ...BalanceSheetModels(sequelize),
};

if (models.gl_account_master) {
  models.gl_account_master.hasMany(models.TrialBalanceReport, {
    foreignKey: "gl_account_id",
    as: "trial_balance_reports",
  });

  models.gl_account_master.hasMany(models.PLReport, {
    foreignKey: "gl_account_id",
    as: "profit_loss_reports",
  });

  models.gl_account_master.hasMany(models.BSReport, {
    foreignKey: "gl_account_id",
    as: "balance_sheet_reports",
  });
}

if (models.TrialBalanceReport) {
  models.TrialBalanceReport.belongsTo(models.gl_account_master, {
    foreignKey: "gl_account_id",
    as: "gl_account",
  });

  models.TrialBalanceReport.hasMany(models.TrialBalanceColumn, {
    foreignKey: "report_id",
    as: "columns",
  });

  models.TrialBalanceReport.hasMany(models.TrialBalanceRow, {
    foreignKey: "report_id",
    as: "rows",
  });
}

if (models.PLReport) {
  models.PLReport.belongsTo(models.gl_account_master, {
    foreignKey: "gl_account_id",
    as: "gl_account",
  });

  models.PLReport.hasMany(models.PLColumn, {
    foreignKey: "report_id",
    as: "columns",
  });

  models.PLReport.hasMany(models.PLRow, {
    foreignKey: "report_id",
    as: "rows",
  });
}

if (models.BSReport) {
  models.BSReport.belongsTo(models.gl_account_master, {
    foreignKey: "gl_account_id",
    as: "gl_account",
  });

  models.BSReport.hasMany(models.BSColumn, {
    foreignKey: "report_id",
    as: "columns",
  });

  models.BSReport.hasMany(models.BSRow, {
    foreignKey: "report_id",
    as: "rows",
  });
}

export const {
  gl_account_master: GLAccountMaster,
  quickbook_account: QuickbookAccount,
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  PLReport,
  PLColumn,
  PLRow,
  BSReport,
  BSColumn,
  BSRow,
} = models;

export { sequelize };
export default models;