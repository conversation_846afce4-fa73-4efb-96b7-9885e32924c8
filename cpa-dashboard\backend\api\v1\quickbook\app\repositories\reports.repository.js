import {
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  PLReport,
  PLColumn,
  PLRow,
  BSReport,
  BSColumn,
  BSRow,
  sequelize,
} from '../models/index.js';
import { 
  create, 
  findById, 
  update, 
  softDelete, 
  findAll, 
  paginate, 
  count 
} from '../utils/database.utils.js';
import { QUICKBOOKS_SEQUELIZE_OPERATORS } from '../utils/constants/config.constants.js';

const createReportFunction = (Model) => (reportData, options = {}) => create(Model, reportData, options);
const createBulkFunction = (Model) => (data, options = {}) => Model.bulkCreate(data, { validate: false, ignoreDuplicates: true, ...options });
const findByIdFunction = (Model) => (id, options = {}) => findById(Model, id, options);
const findByRealmIdFunction = (Model) => (realmId, options = {}) => findAll(Model, { where: { realm_id: realmId }, ...options });

export const reportsRepository = {
  
  createTrialBalanceReport: createReportFunction(TrialBalanceReport),
  createTrialBalanceColumns: createBulkFunction(TrialBalanceColumn),
  createTrialBalanceRows: createBulkFunction(TrialBalanceRow),
  findTrialBalanceReportById: findByIdFunction(TrialBalanceReport),
  findTrialBalanceReportsByRealmId: findByRealmIdFunction(TrialBalanceReport),

  async findOrCreateTrialBalanceColumn(whereConditions, defaults, options = {}) {
    return await TrialBalanceColumn.findOrCreate({
      where: whereConditions,
      defaults: defaults,
      ...options
    });
  },

  
  createProfitLossReport: createReportFunction(PLReport),
  createProfitLossColumns: createBulkFunction(PLColumn),
  createProfitLossRows: createBulkFunction(PLRow),
  findProfitLossReportById: findByIdFunction(PLReport),
  findProfitLossReportsByRealmId: findByRealmIdFunction(PLReport),

  async findOrCreateProfitLossColumn(whereConditions, defaults, options = {}) {
    return await PLColumn.findOrCreate({
      where: whereConditions,
      defaults: defaults,
      ...options
    });
  },

  
  createBalanceSheetReport: createReportFunction(BSReport),
  createBalanceSheetColumns: createBulkFunction(BSColumn),
  createBalanceSheetRows: createBulkFunction(BSRow),
  findBalanceSheetReportById: findByIdFunction(BSReport),
  findBalanceSheetReportsByRealmId: findByRealmIdFunction(BSReport),

  async findOrCreateBalanceSheetColumn(whereConditions, defaults, options = {}) {
    return await BSColumn.findOrCreate({
      where: whereConditions,
      defaults: defaults,
      ...options
    });
  },


  async findPaginatedReports(Model, options = {}) {
    return await paginate(Model, options);
  },

  async countReportsByRealmId(Model, realmId, whereConditions = {}) {
    return await count(Model, {
      where: {
        realm_id: realmId,
        ...whereConditions
      }
    });
  },

  async softDeleteReport(Model, id, options = {}) {
    return await softDelete(Model, id, options);
  },

  async findReportWithRelatedData(Model, id, include = [], options = {}) {
    return await findById(Model, id, {
      include: include,
      ...options
    });
  },

  
  updateReport: (Model, id, updateData, options = {}) => update(Model, id, updateData, options),
  createTransaction: () => sequelize.transaction(),
  commitTransaction: (transaction) => transaction.commit(),
  rollbackTransaction: (transaction) => transaction.rollback(),
  
  deleteReportsByDateRange(Model, realmId, startDate, endDate, options = {}) {
    return Model.destroy({
      where: {
        realm_id: realmId,
        start_period: { [require('sequelize').Op[QUICKBOOKS_SEQUELIZE_OPERATORS.GTE]]: startDate },
        end_period: { [require('sequelize').Op[QUICKBOOKS_SEQUELIZE_OPERATORS.LTE]]: endDate }
      },
      ...options
    });
  }
};

export default reportsRepository;
