import express from 'express';
import { validate<PERSON>oi } from '../middleware/validation.middleware.js';
import { trialBalanceSchema, profitLossSchema, balanceSheetSchema } from '../validators/reports.validator.js';
import * as reportController from '../controllers/reports.controller.js';

const router = express.Router();

const VALIDATION_MIDDLEWARE = {
  trialBalance: validate<PERSON><PERSON>(trialBalanceSchema),
  profitLoss: validate<PERSON><PERSON>(profitLossSchema),
  balanceSheet: validate<PERSON><PERSON>(balanceSheetSchema)
};

router.post('/trial-balance', VALIDATION_MIDDLEWARE.trialBalance, reportController.fetchTrialBalance);
router.post('/profit-loss', VALIDATION_MIDDLEWARE.profitLoss, reportController.fetchProfitLoss);
router.post('/balance-sheet', VALIDATION_MIDDLEWARE.balanceSheet, reportController.fetchBalanceSheet);

router.get('/trial-balance/:realmId', reportController.getTrialBalanceReports);
router.get('/profit-loss/:realmId', reportController.getProfitLossReports);
router.get('/balance-sheet/:realmId', reportController.getBalanceSheetReports);

export default router;