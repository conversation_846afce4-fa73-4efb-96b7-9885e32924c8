import {
  QUIC<PERSON>BOOKS_SERVICE_LOGS,
  QUICKBOOKS_COMMON_LOGS,
} from '../utils/constants/log.constants.js';
import {
  QUI<PERSON><PERSON>BOOKS_DATABASE, 
  QUICKBOOKS_DEFAULTS,
  QUICKBOOKS_STATUS,
  QUICKBOOKS_TABLE_NAMES,
  QUICKBOOKS_TABLE_CONFIG,
  QUICKBOOKS_LINE_ITEM_MAPPING,
  QUICKBOOKS_FIELD_NAMES,
  QUICKBOOKS_HTTP_HEADERS,
  QUICKBOOKS_VALIDATION
} from '../utils/constants/config.constants.js';
import {
  ERROR_MESSAGES,
} from '../utils/constants/error.constants.js';
import { QUICKBOOKS_MESSAGES } from '../utils/constants/error.constants.js';
import { HARDCODED_STRINGS } from '../utils/constants/strings.constants.js';
import * as status from '../utils/status_code.utils.js';
import logger from '../../config/logger.config.js';
import axios from 'axios';
import { decrypt, encrypt } from '../utils/encryption.utils.js';
import { GLAccountMaster } from '../models/index.js';
import { errorResponse } from '../utils/response.util.js';
import knex from 'knex';
import { Parser } from 'json2csv';



// Environment configuration with validation
const QB_ENV = {
  clientID: process.env.QUICKBOOKS_CLIENT_ID,
  clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
  tokenUrl: process.env.QUICKBOOKS_TOKEN_URL,
  baseUrl: process.env.SANDBOX_URL,
  dbServer: process.env.DB_SERVER,
  dbUser: process.env.DB_USER,
  dbPassword: process.env.DB_PASSWORD
};



// Validate critical environment variables
const validateEnvironment = () => {
  const required = HARDCODED_STRINGS.REQUIRED_ENV_VARS;
  const missing = required.filter(key => !QB_ENV[key]);
  if (missing.length > 0) {
    throw new Error(`${HARDCODED_STRINGS.MISSING_REQUIRED_ENV_VARS}: ${missing.join(', ')}`);
  }
};



// Initialize environment validation
try {
  validateEnvironment();
} catch (error) {
  logger.error(QUICKBOOKS_SERVICE_LOGS.DATABASE_CONNECTION_ERROR, { error: error.message });
}



// Cache for database connections to improve performance
const dbConnectionCache = new Map();

/**
 * Creates optimized database configuration with connection pooling
 * @param {string} databaseName - Target database name
 * @returns {Object} Knex configuration object
 */

const createDBConfig = (databaseName) => ({
  client: HARDCODED_STRINGS.DB_CONFIG.CLIENT,
  connection: {
    server: QB_ENV.dbServer || QUICKBOOKS_DATABASE.FALLBACK.SERVER,
    user: QB_ENV.dbUser || QUICKBOOKS_DATABASE.FALLBACK.USER,
    password: QB_ENV.dbPassword || QUICKBOOKS_DATABASE.FALLBACK.PASSWORD,
    database: databaseName,
    options: {
      encrypt: HARDCODED_STRINGS.DB_CONFIG.ENCRYPT,
      trustServerCertificate: HARDCODED_STRINGS.DB_CONFIG.TRUST_SERVER_CERTIFICATE,
      enableArithAbort: HARDCODED_STRINGS.DB_CONFIG.ENABLE_ARITH_ABORT,
      requestTimeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS,
      connectionTimeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS
    }
  },
  pool: {
    min: QUICKBOOKS_DEFAULTS.POOL_MIN,
    max: QUICKBOOKS_DEFAULTS.POOL_MAX,
    acquireTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_ACQUIRE_TIMEOUT,
    idleTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_IDLE_TIMEOUT,
    createTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_CREATE_TIMEOUT,
    destroyTimeoutMillis: QUICKBOOKS_DEFAULTS.POOL_DESTROY_TIMEOUT,
    reapIntervalMillis: QUICKBOOKS_DEFAULTS.POOL_REAP_INTERVAL,
    createRetryIntervalMillis: QUICKBOOKS_DEFAULTS.POOL_RETRY_INTERVAL
  },
  acquireConnectionTimeout: QUICKBOOKS_DEFAULTS.POOL_ACQUIRE_TIMEOUT,
  debug: process.env.NODE_ENV === HARDCODED_STRINGS.NODE_ENV_DEVELOPMENT
});



/**
 * Creates or retrieves cached Knex database instance
 * @param {string} databaseName - Database name
 * @returns {Promise<Object>} Knex instance
 */
export const createKnexInstance = async (databaseName) => {
  if (dbConnectionCache.has(databaseName)) {
    const cachedInstance = dbConnectionCache.get(databaseName);
    try {
      await cachedInstance.raw(HARDCODED_STRINGS.SELECT_1);
      return cachedInstance;
    } catch {
      dbConnectionCache.delete(databaseName);
      await cachedInstance.destroy().catch(error => 
        logger.warn(QUICKBOOKS_COMMON_LOGS.ERROR_DESTROYING_CONNECTION, error.message)
      );
    }
  }

  const config = createDBConfig(databaseName);
  const dbInstance = knex(config);
  
  dbInstance.on(HARDCODED_STRINGS.QUERY_ERROR_EVENT, (error) => {
    const connectionErrors = [
      QUICKBOOKS_DATABASE.QB_DB_CONNECTION_CLOSED,
      QUICKBOOKS_DATABASE.QB_DB_CONNECTION_TIMEOUT,
      QUICKBOOKS_DATABASE.ERROR_TYPES.ETIMEOUT,
      QUICKBOOKS_DATABASE.ERROR_TYPES.ECONNRESET
    ];
    
    const isConnectionError = connectionErrors.some(errorType => 
      error.message.includes(errorType)
    );
    
    if (isConnectionError) {
      logger.error(QUICKBOOKS_SERVICE_LOGS.DATABASE_CONNECTION_ERROR + error.message);
      logger.info(QUICKBOOKS_SERVICE_LOGS.ATTEMPTING_RECONNECT);
      
      dbConnectionCache.delete(databaseName);
      dbInstance.destroy().catch(() => 
        logger.warn(QUICKBOOKS_COMMON_LOGS.ERROR_DESTROYING_OLD_CONNECTION)
      );
    }
  });

  dbConnectionCache.set(databaseName, dbInstance);
  return dbInstance;
};



/**
 * Retries database queries with exponential backoff
 * @param {Function} queryFn - Query function to execute
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} delay - Base delay in milliseconds
 * @returns {Promise<*>} Query result
 */
export const retryQuery = async (
  queryFn, 
  maxRetries = QUICKBOOKS_DEFAULTS.MAX_RETRIES, 
  delay = QUICKBOOKS_DEFAULTS.RETRY_DELAY_MS
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await queryFn();
    } catch (error) {
      const retryableErrors = [
        QUICKBOOKS_DATABASE.ERROR_TYPES.TIMEOUT,
        QUICKBOOKS_DATABASE.ERROR_TYPES.ETIMEOUT,
        QUICKBOOKS_DATABASE.ERROR_TYPES.ECONNRESET
      ];
      
      const isRetryable = retryableErrors.some(errorType => 
        error.message.includes(errorType)
      );
      
      if (isRetryable && attempt < maxRetries) {
        logger.warn(QUICKBOOKS_SERVICE_LOGS.RETRYING_QUERY(attempt, maxRetries));
        await new Promise(resolve => setTimeout(resolve, delay * attempt)); // Exponential backoff
      } else {
        logger.error(QUICKBOOKS_SERVICE_LOGS.QUERY_FAILED(attempt, error.message));
        throw error;
      }
    }
  }
};



/**
 * Creates standardized QuickBooks API request configuration
 * @param {string} url - API endpoint URL
 * @param {string} accessToken - QuickBooks access token
 * @returns {Promise<Object>} Axios response
 */



const createQuickBooksRequest = async (url, accessToken) => {
  return axios.get(url, {
    headers: {
      [QUICKBOOKS_HTTP_HEADERS.AUTHORIZATION]: `Bearer ${accessToken}`,
      Accept: QUICKBOOKS_HTTP_HEADERS.ACCEPT
    },
    timeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS
  });
};



/**
 * Exchanges authorization code for access tokens
 * @param {string} code - Authorization code from QuickBooks
 * @returns {Promise<Object>} Token data object
 */



export const exchangeAuthCodeForTokens = async (code) => {
  try {
    const authHeader = Buffer.from(`${QB_ENV.clientID}:${QB_ENV.clientSecret}`).toString(HARDCODED_STRINGS.STRING_OPS.BASE64);
    
    const response = await axios.post(
      QB_ENV.tokenUrl,
      new URLSearchParams({
        grant_type: QUICKBOOKS_DEFAULTS.GRANT_TYPE_AUTH_CODE,
        code,
        redirect_uri: process.env.REDIRECT_URI
      }).toString(),
      {
        headers: {
          'Content-Type': HARDCODED_STRINGS.HTTP_HEADERS.CONTENT_TYPE_FORM,
          [QUICKBOOKS_HTTP_HEADERS.AUTHORIZATION]: `${QUICKBOOKS_DEFAULTS.AUTH_BASIC_PREFIX}${authHeader}`
        },
        timeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS
      }
    );
    
    return response.data;
  } catch (error) {
    logger.error(QUICKBOOKS_SERVICE_LOGS.TOKEN_EXCHANGE_FAILED, error.message);
    throw error;
  }
};



/**
 * Refreshes QuickBooks access tokens
 * @param {Object} quickbookAccount - QuickBooks account object
 * @returns {Promise<Object|null>} Updated account or null if failed
 */



export const refreshTokens = async (quickbookAccount) => {
  try {
    if (!quickbookAccount?.id || !quickbookAccount?.refresh_token) {
      throw new Error(QUICKBOOKS_MESSAGES.ACCOUNT_NOT_FOUND);
    }
    
    const refreshToken = await decrypt(quickbookAccount.refresh_token);
    if (!refreshToken) {
      throw new Error(HARDCODED_STRINGS.FAILED_TO_DECRYPT_REFRESH_TOKEN);
    }
    
    const authString = Buffer.from(`${QB_ENV.clientID}:${QB_ENV.clientSecret}`).toString(HARDCODED_STRINGS.STRING_OPS.BASE64);
    
    const tokenResponse = await axios.post(
      QB_ENV.tokenUrl,
      new URLSearchParams({
        grant_type: QUICKBOOKS_DEFAULTS.GRANT_TYPE_REFRESH,
        refresh_token: refreshToken
      }),
      {
        headers: {
          [QUICKBOOKS_HTTP_HEADERS.AUTHORIZATION]: `${QUICKBOOKS_DEFAULTS.AUTH_BASIC_PREFIX}${authString}`,
          'Content-Type': HARDCODED_STRINGS.HTTP_HEADERS.CONTENT_TYPE_FORM
        },
        timeout: QUICKBOOKS_DEFAULTS.REQUEST_TIMEOUT_MS
      }
    );
    
    if (!tokenResponse.data?.access_token || !tokenResponse.data?.refresh_token) {
      throw new Error(QUICKBOOKS_MESSAGES.INVALID_TOKEN_RESPONSE_FROM_QUICKBOOKS);
    }
    
    const [encryptedRefreshToken, encryptedAccessToken] = await Promise.all([
      encrypt(tokenResponse.data.refresh_token),
      encrypt(tokenResponse.data.access_token)
    ]);
    
    const updateData = {
      [QUICKBOOKS_FIELD_NAMES.REFRESH_TOKEN]: encryptedRefreshToken,
      [QUICKBOOKS_FIELD_NAMES.ACCESS_TOKEN]: encryptedAccessToken,
      [QUICKBOOKS_FIELD_NAMES.TOKEN_EXPIRY_TIME]: new Date(Date.now() + QUICKBOOKS_DEFAULTS.TOKEN_EXPIRY_MS),
      [QUICKBOOKS_FIELD_NAMES.UPDATED_AT]: new Date()
    };
    
    await GLAccountMaster.update(updateData, { where: { id: quickbookAccount.id } });
    const updatedUser = await GLAccountMaster.findByPk(quickbookAccount.id);
    
    logger.info(QUICKBOOKS_SERVICE_LOGS.TOKENS_REFRESHED_SUCCESS(quickbookAccount.id));
    return updatedUser;
    
  } catch (error) {
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_ADDING_TOKEN, error.message);
    if (error.response) {
      logger.error(QUICKBOOKS_SERVICE_LOGS.QUICKBOOKS_API_ERROR, {
        status: error.response.status,
        data: error.response.data
      });
    }
    return null;
  }
};



/**
 * Fetches data from QuickBooks API for a specific table
 * @param {string} tableName - QuickBooks table name
 * @param {Object} user - User account object
 * @returns {Promise<Array>} Data array from QuickBooks
 */



export const fetchData = async (tableName, user) => {
  try {
    let accessToken = await decrypt(user.access_token);
    const parentTable = QUICKBOOKS_LINE_ITEM_MAPPING[tableName] || tableName;
    
    const url = `${QB_ENV.baseUrl}/v3/company/${user.realm_id}/query?query=SELECT * FROM ${parentTable}`;
    const response = await createQuickBooksRequest(url, accessToken);
    
    const responseData = response.data.QueryResponse?.[parentTable];
    return responseData || [];
    
  } catch (error) {
    if (error.response?.status === status.STATUS_CODE_UNAUTHORIZED) {
      logger.warn(QUICKBOOKS_SERVICE_LOGS.RETRY_FOR_TOKEN_TABLE(tableName));
      
      const updatedUser = await refreshTokens(user);
      if (!updatedUser) {
        throw new Error(QUICKBOOKS_MESSAGES.FAILED_TO_REFRESH_ACCESS_TOKEN);
      }
      
      const newAccessToken = await decrypt(updatedUser.access_token);
      const parentTable = QUICKBOOKS_LINE_ITEM_MAPPING[tableName] || tableName;
      const url = `${QB_ENV.baseUrl}/v3/company/${user.realm_id}/query?query=SELECT * FROM ${parentTable}`;
      
      const retryResponse = await createQuickBooksRequest(url, newAccessToken);
      const retryResponseData = retryResponse.data.QueryResponse?.[parentTable];
      return retryResponseData || [];
    }
    
    logger.error(QUICKBOOKS_SERVICE_LOGS.ERROR_FETCHING_DATA(tableName, error.message));
    throw error;
  }
};



/**
 * Saves content to local file system with proper error handling
 * @param {string} fileName - File name
 * @param {string} content - File content
 * @param {string} directory - Target directory
 * @returns {Promise<string>} File path
 */



export const saveToLocalStorage = async (
  fileName, 
  content, 
  directory = QUICKBOOKS_DEFAULTS.DEFAULT_UPLOAD_DIR
) => {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const uploadDir = path.join(process.cwd(), directory);
    await fs.mkdir(uploadDir, { recursive: true });
    
    const filePath = path.join(uploadDir, fileName);
    await fs.writeFile(filePath, content, HARDCODED_STRINGS.FILE_SYSTEM.UTF8);
    
    logger.info(QUICKBOOKS_SERVICE_LOGS.FILE_SAVED_SUCCESS(fileName));
    return `/${directory}/${fileName}`;
    
  } catch (error) {
    logger.error(QUICKBOOKS_SERVICE_LOGS.FILE_SAVE_ERROR(fileName, error.message));
    throw error;
  }
};



/**
 * Flattens nested JSON objects for CSV conversion
 * @param {Array} data - Array of objects to flatten
 * @returns {Array} Flattened array
 */



export const flattenJson = (data) => {
  if (!Array.isArray(data)) {
    throw new Error(QUICKBOOKS_MESSAGES.DATA_MUST_BE_ARRAY);
  }
  return data.map(entry => flattenObject(entry));
};



/**
 * Recursively flattens a single object
 * @param {Object} obj - Object to flatten
 * @param {string} parentKey - Parent key for nested properties
 * @param {Object} result - Result accumulator
 * @returns {Object} Flattened object
 */



const flattenObject = (obj, parentKey = '', result = {}) => {
  if (obj === null || obj === undefined) {
    return result;
  }
  
  Object.keys(obj).forEach(key => {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) return;
    
    const newKey = parentKey ? `${parentKey}_${key}` : key;
    const value = obj[key];
    
    if (value === null || value === undefined) {
      result[newKey] = null;
    } else if (Array.isArray(value)) {
      result[newKey] = JSON.stringify(value);
    } else if (typeof value === 'object') {
      flattenObject(value, newKey, result);
    } else {
      result[newKey] = typeof value === 'string' ? value.replace(/'/g, HARDCODED_STRINGS.STRING_OPS.SINGLE_QUOTE_REPLACEMENT) : value;
    }
  });
  
  return result;
};



/**
 * Ensures database table exists with proper schema
 * @param {string} databaseName - Database name
 * @param {string} tableName - Table name
 * @param {Array} columns - Column definitions
 * @param {string} primaryKey - Primary key column
 * @param {string} foreignKey - Foreign key column
 * @returns {Promise<void>}
 */



export const ensureTableExists = async (databaseName, tableName, columns, primaryKey, foreignKey) => {
  const targetDb = await createKnexInstance(databaseName);
  if (!targetDb) {
    throw new Error(QUICKBOOKS_MESSAGES.DATABASE_CONNECTION_NOT_ESTABLISHED);
  }
  
  const tableExists = await targetDb.schema.hasTable(tableName);
  if (!tableExists) {
    logger.info(QUICKBOOKS_SERVICE_LOGS.CREATING_TABLE(tableName));
    
    await targetDb.schema.createTable(tableName, (table) => {
      columns.forEach((col) => {
        if (col.toLowerCase() !== primaryKey.toLowerCase()) {
          table.text(col, QUICKBOOKS_VALIDATION.FIELD_LENGTHS.MAX_TEXT_LENGTH);
        }
      });
      
      table.string(primaryKey, QUICKBOOKS_VALIDATION.FIELD_LENGTHS.MAX_STRING_LENGTH).primary();
      table.increments(QUICKBOOKS_FIELD_NAMES.ARITEK_ID).notNullable().unique();
      
      if (foreignKey !== primaryKey) {
        table.string(foreignKey, QUICKBOOKS_VALIDATION.FIELD_LENGTHS.MAX_STRING_LENGTH);
      }
      
      table.boolean(QUICKBOOKS_FIELD_NAMES.IS_DELETED).defaultTo(false);
      table.timestamps(true, true);
    });
  }
};



/**
 * Performs optimized upsert operations with batch processing
 * @param {string} databaseName - Database name
 * @param {string} tableName - Table name
 * @param {Array} data - Data to upsert
 * @param {string} primaryKey - Primary key column
 * @param {string} quickbookAccountId - QuickBooks account ID
 * @returns {Promise<void>}
 */



export const upsertData = async (databaseName, tableName, data, primaryKey, quickbookAccountId) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    logger.warn(QUICKBOOKS_SERVICE_LOGS.NO_DATA_FOR_UPSERT(tableName));
    return;
  }

  const targetDb = await createKnexInstance(databaseName);
  
  await retryQuery(async () => {
    const enrichedData = data.map(row => ({
      ...row,
      [QUICKBOOKS_FIELD_NAMES.QUICKBOOK_ACCOUNT_ID]: quickbookAccountId
    }));
    
    const columns = Object.keys(enrichedData[0]);
    const columnList = columns.map(col => `[${col}]`).join(', ');
    const updateList = columns
      .filter(col => col !== primaryKey)
      .map(col => `[Target].[${col}] = [Source].[${col}]`)
      .join(', ');
    


    // Process in batches for better performance



    for (let i = 0; i < enrichedData.length; i += QUICKBOOKS_DEFAULTS.BATCH_SIZE) {
      const batch = enrichedData.slice(i, i + QUICKBOOKS_DEFAULTS.BATCH_SIZE);
      const values = batch
        .map(row => 
          `(${columns.map(col => 
            `'${row[col] ? row[col].toString().replace(/'/g, "''") : ''}'`
          ).join(', ')})`
        )
        .join(', ');
      
      const sql = `
        MERGE INTO ${tableName} AS Target
        USING (VALUES ${values})
        AS Source (${columnList})
        ON Target.[${primaryKey}] = Source.[${primaryKey}]
        WHEN MATCHED THEN
          UPDATE SET ${updateList}
        WHEN NOT MATCHED THEN
          INSERT (${columnList}) VALUES (${columns.map(col => `Source.[${col}]`).join(', ')});
      `;
      
      await targetDb.raw(sql);
      logger.info(QUICKBOOKS_SERVICE_LOGS.BATCH_PROCESSED(Math.floor(i / QUICKBOOKS_DEFAULTS.BATCH_SIZE) + 1, tableName));
    }
  });
};



/**
 * Syncs all QuickBooks data for an account
 * @param {Object} quickbookAccount - QuickBooks account object
 * @param {string} organizationId - Organization ID
 * @param {string} initiatedBy - User or system that initiated the sync
 * @returns {Promise<Array>} Array of uploaded files
 */



export const syncAccountData = async (quickbookAccount, organizationId, initiatedBy) => {
  const databaseName = `${QUICKBOOKS_DEFAULTS.DATABASE_NAME_PREFIX}${organizationId}${QUICKBOOKS_DEFAULTS.DATABASE_NAME_SUFFIX}`;
  const uploadedFiles = [];
  let tokenRefreshed = false;
  
  for (const tableName of QUICKBOOKS_TABLE_NAMES) {
    const tableConfig = QUICKBOOKS_TABLE_CONFIG[tableName];
    if (!tableConfig?.primaryKey) {
      logger.warn(QUICKBOOKS_SERVICE_LOGS.NO_TABLE_MAPPING(tableName));
      continue;
    }
    
    const { sqlTableName, primaryKey } = tableConfig;
    
    try {
      let fileData = await fetchData(tableName, quickbookAccount);
      


      // Refresh account data if tokens were updated



      if (!tokenRefreshed) {
        quickbookAccount = await GLAccountMaster.findByPk(quickbookAccount.id);
        tokenRefreshed = true;
      }
      
      if (!fileData.length) {
        logger.warn(QUICKBOOKS_SERVICE_LOGS.NO_DATA_FOUND_TABLE(tableName));
        continue;
      }
      
      const flattenedData = flattenJson(fileData);
      const columns = Object.keys(flattenedData[0]);
      
      await ensureTableExists(databaseName, sqlTableName, columns, primaryKey, QUICKBOOKS_FIELD_NAMES.QUICKBOOK_ACCOUNT_ID);
      await upsertData(databaseName, sqlTableName, flattenedData, primaryKey, quickbookAccount.id);
      


      // Generate CSV file



      const parser = new Parser();
      const csvData = parser.parse(flattenedData);
      const fileName = `${sqlTableName}.csv`;
      
      const fileUrl = await saveToLocalStorage(
        fileName,
        csvData,
        `${QUICKBOOKS_DEFAULTS.UPLOAD_DIRECTORY_PREFIX}${organizationId}`
      );
      
      uploadedFiles.push({ tableName, fileUrl });
      
    } catch (error) {
      if (error.response?.status === status.STATUS_CODE_UNAUTHORIZED) {
        await logQuickBookSync(initiatedBy, QUICKBOOKS_STATUS.FAILED);
        throw error;
      }
      logger.error(QUICKBOOKS_SERVICE_LOGS.ERROR_PROCESSING_TABLE(tableName, error.message));
      throw error;
    }
  }
  
  if (uploadedFiles.length === 0) {
    throw new Error(QUICKBOOKS_MESSAGES.NO_DATA_FOUND);
  }
  
  return uploadedFiles;
};



/**
 * Logs QuickBooks synchronization events
 * @param {string} initiatedBy - User or system that initiated the sync
 * @param {string} syncStatus - Sync status (completed/failed)
 * @param {string} quickbookAccountId - QuickBooks account ID (optional)
 * @returns {Promise<void>}
 */



export const logQuickBookSync = async (initiatedBy, syncStatus, quickbookAccountId = null) => {
  try {
    const logEntry = {
      initiated_by: initiatedBy,
      status: syncStatus,
      created_at: new Date()
    };
    
    if (quickbookAccountId) {
      logEntry[QUICKBOOKS_FIELD_NAMES.QUICKBOOK_ACCOUNT_ID] = quickbookAccountId;
    }
    
    logger.info(QUICKBOOKS_SERVICE_LOGS.SYNC_LOG_CREATED(syncStatus, initiatedBy, quickbookAccountId));
    
  } catch (error) {
    logger.error(QUICKBOOKS_SERVICE_LOGS.ERROR_CREATING_SYNC_LOG(error.message));
  }
};



/**
 * Handles sync failures with proper error logging
 * @param {string} initiatedBy - User or system that initiated the sync
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Error message
 * @param {Object} res - Express response object
 * @param {string} quickbookAccountId - QuickBooks account ID (optional)
 * @returns {Promise<Object>} HTTP response
 */



export const handleSyncFailure = async (initiatedBy, statusCode, message, res, quickbookAccountId = null) => {
  try {
    await logQuickBookSync(initiatedBy, QUICKBOOKS_STATUS.FAILED, quickbookAccountId);
    logger.error(QUICKBOOKS_ERROR_LOGS.ERROR_UPLOADING_FILE + ': ' + message);
    
    if (res && !res.headersSent) {
      return res.status(statusCode).json(errorResponse(message));
    }
    
  } catch (error) {
    logger.error(QUICKBOOKS_SERVICE_LOGS.ERROR_IN_HANDLE_SYNC_FAILURE(error.message));
    if (res && !res.headersSent) {
      return res.status(status.STATUS_CODE_INTERNAL_SERVER_ERROR)
                .json(errorResponse(ERROR_MESSAGES.INTERNAL_SERVER_ERROR));
    }
  }
};



// Legacy function aliases for backward compatibility
export const getTokensDirectly = refreshTokens;
export const getQuickBooksData = fetchData;

export default {
  createKnexInstance,
  retryQuery,
  exchangeAuthCodeForTokens,
  refreshTokens,
  fetchData,
  saveToLocalStorage,
  flattenJson,
  ensureTableExists,
  upsertData,
  syncAccountData,
  logQuickBookSync,
  handleSyncFailure,


  // Legacy aliases



  getTokensDirectly: refreshTokens
};