import * as status from '../utils/status_code.utils.js';
import logger from '../../config/logger.config.js';
import {
  QUICKBOOKS_REPORTS_LOGS,
} from '../utils/constants/log.constants.js';
import { HARDCODED_STRINGS } from '../utils/constants/strings.constants.js';
import axios from 'axios';
import { decrypt } from '../utils/encryption.utils.js';
import { formatDateOnly, createValidatedDateRange } from '../utils/date.utils.js';
import reportsRepository from '../repositories/reports.repository.js';
import { getTokensDirectly } from './quickbooks.service.js';

const BASE_URL = process.env.SANDBOX_URL;
const BATCH_SIZE = 1000;




// API Configuration

const API_CONFIG = {
  headers: { Accept: 'application/json' },
  timeout: 30000,
  retries: 1
};




// Helper functions for cleaner code

const createEndpoint = (realmId, reportName, startDate, endDate) => 
  `/v3/company/${realmId}/reports/${reportName}?start_date=${startDate}&end_date=${endDate}&summarize_column_by=Month&minorversion=75`;

const createAuthHeaders = (token) => ({
  ...API_CONFIG.headers,
  Authorization: `Bearer ${token}`
});




/**
 * Execute API request with retry logic
 */

const executeApiRequest = async (url, headers, reportName) => {
  try {
    const response = await axios.get(url, { headers, timeout: API_CONFIG.timeout });
    logger.info(QUICKBOOKS_REPORTS_LOGS.FETCH_SUCCESS, { 
      reportName, 
      dataSize: JSON.stringify(response.data).length 
    });
    return response.data;
  } catch (error) {
    logger.error(QUICKBOOKS_REPORTS_LOGS.API_REQUEST_FAILED, { 
      reportName, 
      error: error.message,
      status: error.response?.status 
    });
    throw error;
  }
};




/**
 * Handle token refresh and retry
 */

const handleTokenRetry = async (quickbookAccount, reportName, startDate, endDate) => {
  logger.warn(QUICKBOOKS_REPORTS_LOGS.RETRY_FOR_TOKEN);
  
  const updatedUser = await getTokensDirectly(quickbookAccount);
  if (!updatedUser) {
    throw new Error(HARDCODED_STRINGS.REPORT_DEFAULTS.FAILED_TO_REFRESH_TOKEN);
  }

  const newAccessToken = await decrypt(updatedUser.access_token);
  const endpoint = createEndpoint(quickbookAccount.realm_id, reportName, startDate, endDate);
  const url = `${BASE_URL}${endpoint}`;
  
  const response = await executeApiRequest(url, createAuthHeaders(newAccessToken), reportName);
  logger.info(QUICKBOOKS_REPORTS_LOGS.RETRY_SUCCESS, { reportName });
  
  return response;
};




/**
 * Process column data efficiently
 */




const processColumns = (columns, reportId, realmId) => {
  const colMap = new Map();
  const columnData = [];
  let colIndex = 0;

  columns.forEach((col) => {
    const processColData = (subCol, parentTitle = null) => {
      const columnInfo = {
        report_id: reportId,
        col_title: subCol.ColTitle || col.ColTitle,



        col_type: subCol.ColType || col.ColType || 'Money',

        col_type: subCol.ColType || col.ColType || HARDCODED_STRINGS.REPORT_DEFAULTS.COL_TYPE,


        col_type: subCol.ColType || col.ColType || HARDCODED_STRINGS.REPORT_DEFAULTS.COL_TYPE,
        col_type: subCol.ColType || col.ColType || HARDCODED_STRINGS.REPORT_DEFAULTS.COL_TYPE,

        col_id: subCol.id || col.ColId || null,
        parent_col_id: null,
        col_order: colIndex,
        created_at: new Date(),
        updated_at: new Date(),
        parent_col_title: parentTitle,
        realm_id: realmId
      };

      columnData.push(columnInfo);
      colMap.set(colIndex, {
        col_title: columnInfo.col_title,
        parent_col_title: parentTitle,
        col_type: columnInfo.col_type
      });
      colIndex++;
    };

    if (col.ColData?.length) {
      const parentMeta = Object.fromEntries(
        (col.MetaData || []).map(m => [m.Name, m.Value])
      );
      
      col.ColData.forEach((subCol) => {
        const _columnInfo = processColData(subCol, col.ColTitle);



        // Add period data for trial balance




        if (parentMeta.StartDate) {
          columnData[columnData.length - 1].period_start = formatDateOnly(parentMeta.StartDate);
          columnData[columnData.length - 1].period_end = formatDateOnly(parentMeta.EndDate);
        }
      });
    } else {
      processColData(col);
    }
  });

  return { columnData, colMap };
};




/**
 * Process row data efficiently
 */




const processRows = (rows, reportId, realmId, colMap, rowDataTemplate) => {
  const rowData = [];

  rows.forEach((row) => {



    if (row.type !== 'Data' || !row.ColData?.length) return;

    if (row.type !== HARDCODED_STRINGS.REPORT_DEFAULTS.ROW_TYPE_DATA || !row.ColData?.length) return;


    if (row.type !== HARDCODED_STRINGS.REPORT_DEFAULTS.ROW_TYPE_DATA || !row.ColData?.length) return;
    if (row.type !== HARDCODED_STRINGS.REPORT_DEFAULTS.ROW_TYPE_DATA || !row.ColData?.length) return;

    
    const accountCol = row.ColData[0];
    const accountId = accountCol?.id || null;
    const accountName = accountCol?.value || null;

    row.ColData.slice(1).forEach((colData, i) => {
      if (!colData?.value) return;
      
      const value = parseFloat(colData.value) || 0;
      if (value === 0) return;
      
      const columnIndex = i + 1;
      const columnInfo = colMap.get(columnIndex);
      
      if (columnInfo) {
        rowData.push({
          ...rowDataTemplate,
          report_id: reportId,
          account_name: accountName,
          column_index: columnIndex,
          value: value,
          created_at: new Date(),
          updated_at: new Date(),
          col_title: columnInfo.col_title,
          parent_col_title: columnInfo.parent_col_title,
          col_type: columnInfo.col_type,
          realm_id: realmId,



          // Conditional properties based on report type




          ...(rowDataTemplate.quickbooks_account_id !== undefined && { quickbooks_account_id: accountId }),
          ...(rowDataTemplate.account_id !== undefined && { account_id: accountId })
        });
      }
    });
  });

  return rowData;
};




/**
 * Save report data with optimized batch processing
 */




const saveReportData = async (mappedData, createReportFn, createColumnsFn, createRowsFn, reportType) => {
  const transaction = await reportsRepository.createTransaction();
  
  try {
    logger.info(QUICKBOOKS_REPORTS_LOGS.SAVING_DATA, { 
      reportType,
      columnsCount: mappedData.columnData.length,
      rowsCount: mappedData.rowData.length
    });




    // Create report
    const report = await createReportFn(mappedData.reportData, { transaction });

    // Create columns with mapping


    const columnMap = new Map();
    const columnPromises = mappedData.columnData.map(async (column, index) => {
      const [columnRecord] = await reportsRepository[`findOrCreate${reportType}Column`]?.(
        {
          report_id: report.id,
          col_title: column.col_title,
          col_type: column.col_type,
          col_order: column.col_order,
        },
        {
          ...column,
          report_id: report.id,
        },
        { transaction }
      ) || [await createColumnsFn([{ ...column, report_id: report.id }], { transaction })];
      
      columnMap.set(index, columnRecord.id);
      return columnRecord;
    });

    await Promise.all(columnPromises);




    // Prepare and create rows in batches




    const rowsToCreate = mappedData.rowData
      .map(row => {
        const columnId = Array.from(columnMap.entries())
          .find(([index, _]) => {
            const originalColumn = mappedData.columnData[index];
            return originalColumn.col_title === row.col_title && 
                   originalColumn.parent_col_title === row.parent_col_title &&
                   originalColumn.col_type === row.col_type;
          })?.[1];

        return columnId ? { ...row, report_id: report.id, column_id: columnId } : null;
      })
      .filter(Boolean);




    // Batch insert rows




    const batchPromises = [];
    for (let i = 0; i < rowsToCreate.length; i += BATCH_SIZE) {
      const batch = rowsToCreate.slice(i, i + BATCH_SIZE);
      batchPromises.push(createRowsFn(batch, { transaction }));
    }

    await Promise.all(batchPromises);
    await reportsRepository.commitTransaction(transaction);

    logger.info(QUICKBOOKS_REPORTS_LOGS.SAVE_SUCCESS, {
      reportType,
      reportId: report.id,
      columnsCount: mappedData.columnData.length,
      rowsCount: rowsToCreate.length
    });

    return {
      reportId: report.id,
      columnsCount: mappedData.columnData.length,
      rowsCount: rowsToCreate.length
    };

  } catch (error) {
    await reportsRepository.rollbackTransaction(transaction);
    logger.error(QUICKBOOKS_REPORTS_LOGS.SAVE_FAILED, { reportType, error: error.message });
    throw error;
  }
};

export const reportsService = {



  /**
   * Get report data from QuickBooks API with retry logic
   */




  async getReportDataFromQuickBooks(quickbookAccount, startDate, endDate, reportName, quickBookAccessToken = null) {
    const ACCESS_TOKEN = quickBookAccessToken || await decrypt(quickbookAccount.access_token);
    const endpoint = createEndpoint(quickbookAccount.realm_id, reportName, startDate, endDate);
    const url = `${BASE_URL}${endpoint}`;
    
    logger.info(QUICKBOOKS_REPORTS_LOGS.FETCHING, { 
      reportName, startDate, endDate, realmId: quickbookAccount.realm_id 
    });

    try {
      return await executeApiRequest(url, createAuthHeaders(ACCESS_TOKEN), reportName);
    } catch (error) {



      // Only retry on unauthorized errors




      if (error.response?.status === status.STATUS_CODE_UNAUTHORIZED) {
        return await handleTokenRetry(quickbookAccount, reportName, startDate, endDate);
      }
      throw error;
    }
  },




  /**
   * Map trial balance data with optimized processing
   */




  mapTrialBalanceToOptimizedStructure(quickbooksData, reportId, realmId, fallbackStartDate = null, fallbackEndDate = null) {
    try {
      const header = quickbooksData.QueryResponse?.Header || {};
      const { startDate: startPeriod, endDate: endPeriod } = createValidatedDateRange(
        header.StartPeriod, header.EndPeriod, fallbackStartDate, fallbackEndDate
      );

      const reportData = {
        time: header.Time ? new Date(header.Time) : new Date(),



        report_name: header.ReportName || 'TrialBalance',
        report_basis: header.ReportBasis || 'Accrual',
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || 'Month',
        currency: header.Currency || 'USD',

        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_TRIAL_BALANCE,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,


        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_TRIAL_BALANCE,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,
        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_TRIAL_BALANCE,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,

        realm_id: realmId
      };

      const columns = quickbooksData.QueryResponse?.Columns?.Column || [];
      const { columnData, colMap } = processColumns(columns, reportId, realmId);

      const rows = quickbooksData.QueryResponse?.Rows?.Row || [];
      const rowDataTemplate = {
        quickbooks_account_id: null,
        acct_num: null,
        class_id: null,
        account_type: null
      };
      const rowData = processRows(rows, reportId, realmId, colMap, rowDataTemplate);

      logger.info(QUICKBOOKS_REPORTS_LOGS.MAPPING_SUCCESS, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE,
        columnsCount: columnData.length,
        rowsCount: rowData.length
      });

      return { reportData, columnData, rowData };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, { reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE, error: error.message });
      throw error;
    }
  },




  /**
   * Map profit & loss data with optimized processing
   */




  mapProfitLossToOptimizedStructure(quickbooksData, reportId, realmId, fallbackStartDate = null, fallbackEndDate = null) {
    try {
      const header = quickbooksData.Header || {};
      const { startDate: startPeriod, endDate: endPeriod } = createValidatedDateRange(



        header.StartPeriod || fallbackStartDate, header.EndPeriod || fallbackEndDate, HARDCODED_STRINGS.REPORT_DEFAULTS.PERIOD_DATES

      );

      const reportData = {
        time: formatDateOnly(header.Time),



        report_name: header.ReportName || 'ProfitAndLoss',
        report_basis: header.ReportBasis || 'Accrual',
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || 'Month',
        currency: header.Currency || 'USD',

        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_PROFIT_LOSS,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,


        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_PROFIT_LOSS,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,
        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_PROFIT_LOSS,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,

        realm_id: realmId
      };

      const columns = quickbooksData.Columns?.Column || [];
      const { columnData, colMap } = processColumns(columns, reportId, realmId);

      const rows = quickbooksData.Rows?.Row || [];
      const rowDataTemplate = {
        account_id: null,
        category: null,
        s2_gr3: null,
        account_type: null
      };
      const rowData = processRows(rows, reportId, realmId, colMap, rowDataTemplate);

      logger.info(QUICKBOOKS_REPORTS_LOGS.MAPPING_SUCCESS, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS,
        columnsCount: columnData.length,
        rowsCount: rowData.length
      });

      return { reportData, columnData, rowData };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, { reportType: HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS, error: error.message });
      throw error;
    }
  },




  /**
   * Map balance sheet data with optimized processing
   */




  mapBalanceSheetToOptimizedStructure(quickbooksData, reportId, realmId, fallbackStartDate = null, fallbackEndDate = null) {
    try {
      const header = quickbooksData.Header || {};
      const { startDate: startPeriod, endDate: endPeriod } = createValidatedDateRange(



        header.StartPeriod || fallbackStartDate, header.EndPeriod || fallbackEndDate, HARDCODED_STRINGS.REPORT_DEFAULTS.PERIOD_DATES

      );

      const reportData = {
        time: formatDateOnly(header.Time),



        report_name: header.ReportName || 'BalanceSheet',
        report_basis: header.ReportBasis || 'Accrual',
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || 'Month',
        currency: header.Currency || 'USD',

        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_BALANCE_SHEET,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,


        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_BALANCE_SHEET,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,
        report_name: header.ReportName || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_NAME_BALANCE_SHEET,
        report_basis: header.ReportBasis || HARDCODED_STRINGS.REPORT_DEFAULTS.REPORT_BASIS,
        start_period: startPeriod,
        end_period: endPeriod,
        summarize_columns_by: header.SummarizeColumnsBy || HARDCODED_STRINGS.REPORT_DEFAULTS.SUMMARIZE_COLUMNS_BY,
        currency: header.Currency || HARDCODED_STRINGS.REPORT_DEFAULTS.CURRENCY,

        realm_id: realmId
      };

      const columns = quickbooksData.Columns?.Column || [];
      const { columnData, colMap } = processColumns(columns, reportId, realmId);

      const rows = quickbooksData.Rows?.Row || [];
      const rowDataTemplate = {
        account_id: null,
        classification: null,
        account_type: null,
        account_sub_type: null
      };
      const rowData = processRows(rows, reportId, realmId, colMap, rowDataTemplate);

      logger.info(QUICKBOOKS_REPORTS_LOGS.MAPPING_SUCCESS, {
        reportType: HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET,
        columnsCount: columnData.length,
        rowsCount: rowData.length
      });

      return { reportData, columnData, rowData };
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.MAPPING_FAILED, { reportType: HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET, error: error.message });
      throw error;
    }
  },




  /**
   * Save trial balance data with optimized structure
   */




  async saveTrialBalanceData(mappedData) {
    return saveReportData(
      mappedData,
      reportsRepository.createTrialBalanceReport,
      reportsRepository.createTrialBalanceColumns,
      reportsRepository.createTrialBalanceRows,
      HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE
    );
  },




  /**
   * Save profit & loss data with optimized structure
   */




  async saveProfitLossData(mappedData) {
    return saveReportData(
      mappedData,
      reportsRepository.createProfitLossReport,
      reportsRepository.createProfitLossColumns,
      reportsRepository.createProfitLossRows,
      HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS
    );
  },




  /**
   * Save balance sheet data with optimized structure
   */




  async saveBalanceSheetData(mappedData) {
    return saveReportData(
      mappedData,
      reportsRepository.createBalanceSheetReport,
      reportsRepository.createBalanceSheetColumns,
      reportsRepository.createBalanceSheetRows,
      HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET
    );
  },




  // Report retrieval operations




  async getTrialBalanceReportsByRealmId(realmId, options = {}) {
    try {
      return await reportsRepository.findTrialBalanceReportsByRealmId(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED, { reportType: HARDCODED_STRINGS.REPORT_TYPES.TRIAL_BALANCE, realmId, error: error.message });
      throw error;
    }
  },

  async getProfitLossReportsByRealmId(realmId, options = {}) {
    try {
      return await reportsRepository.findProfitLossReportsByRealmId(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED(HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS, error.message), { reportType: HARDCODED_STRINGS.REPORT_TYPES.PROFIT_AND_LOSS, realmId, error: error.message });
      throw error;
    }
  },

  async getBalanceSheetReportsByRealmId(realmId, options = {}) {
    try {
      return await reportsRepository.findBalanceSheetReportsByRealmId(realmId, options);
    } catch (error) {
      logger.error(QUICKBOOKS_REPORTS_LOGS.RETRIEVAL_FAILED(HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET, error.message), { reportType: HARDCODED_STRINGS.REPORT_TYPES.BALANCE_SHEET, realmId, error: error.message });
      throw error;
    }
  }
};

export default reportsService;




// Legacy exports for backward compatibility




export const getReportData = reportsService.getReportDataFromQuickBooks;
export const mapTrialBalanceToOptimizedStructure = reportsService.mapTrialBalanceToOptimizedStructure;
export const saveTrialBalanceOptimized = reportsService.saveTrialBalanceData;
export const mapProfitLossToOptimizedStructure = reportsService.mapProfitLossToOptimizedStructure;
export const saveProfitLossOptimized = reportsService.saveProfitLossData;
export const mapBalanceSheetToOptimizedStructure = reportsService.mapBalanceSheetToOptimizedStructure;
export const saveBalanceSheetOptimized = reportsService.saveBalanceSheetData;