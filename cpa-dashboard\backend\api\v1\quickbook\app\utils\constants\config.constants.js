


export const CONFIG_DEFAULTS = {
  NODE_ENV: 'development',
  HOST: 'localhost',
  PROTOCOL: 'http',
  PORT: 3000,
  DB_HOST: 'localhost',
  DB_PORT: '5432',
  DB_NAME: 'cpa_dashboard',
  DB_USERNAME: 'postgres',
  JWT_EXPIRATION: '24h',
  REFRESH_TOKEN_EXPIRATION: '7d',
  BCRYPT_ROUNDS: 12,
  COOKIE_MAX_AGE: ********,
  LOG_LEVEL: 'info',
  LOG_FORMAT: 'combined',
  LOG_FILE_PATH: './logs',
  LOG_MAX_SIZE: '20m',
  LOG_MAX_FILES: 14,
  LOG_DIRECTORY: './logs',
  LOG_DATE_PATTERN: 'DD-MM-YYYY',
  LOG_INFO_LEVEL: 'info',
  LOG_ERROR_LEVEL: 'error',
  LOG_ZIPPED_ARCHIVE: true,
  QUICKBOOKS_SCOPE: 'com.intuit.quickbooks.accounting',
  QUICKBOOKS_RESPONSE_TYPE: 'code',
  QUICKBOOKS_ENVIRONMENT_PRODUCTION: 'production',
  QUICKBOOKS_ENVIRONMENT_SANDBOX: 'sandbox',
  QUICKBOOKS_API_VERSION: 'v3',
  MONTH_NAMES: [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ],
  DB_POOL_MAX: 20,
  DB_POOL_MIN: 5,
  DB_POOL_ACQUIRE: 60000,
  DB_POOL_IDLE: 10000
};

export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  STAGING: 'staging',
  TEST: 'test',
};

export const DATE_FORMAT_PATTERNS = {
  YEAR: 'YYYY',
  MONTH: 'MM',
  DAY: 'DD',
  HOURS: 'HH',
  MINUTES: 'mm',
  SECONDS: 'ss',
};

export const ENCRYPTION_DEFAULTS = {
  ALGORITHM: 'aes-256-cbc',
};

export const CONFIG_ENV_TYPES = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  ARRAY: 'array',
  JSON: 'json',
  PRODUCTION: 'production',
  DEVELOPMENT: 'development',
  TEST: 'test',
};

export const CONFIG_VALIDATION = {
  REQUIRED_QUICKBOOKS_VARS: [
    'QUICKBOOKS_CLIENT_ID',
    'QUICKBOOKS_CLIENT_SECRET',
    'QUICKBOOKS_REDIRECT_URI',
    'QUICKBOOKS_TOKEN_URL',
  ],
  ENCRYPTION_KEY_MIN_LENGTH: 32,
};

export const ENCRYPTION_MESSAGES = {
  KEY_MISSING: 'Encryption key is missing or too short',
  ENCRYPTION_FAILED: 'Encryption failed',
  DECRYPTION_FAILED: 'Decryption failed',
};

export const DATE_FORMATS = {
  FORMAT: 'DD-MM-YYYY',
  LOG_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  ISO_DATE_ONLY: 'YYYY-MM-DD',
  ISO_DATETIME: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  QUICKBOOKS_API: 'YYYY-MM-DD',
  DISPLAY_FORMAT: 'DD-MM-YYYY',
  MOMENT_FORMATS: ['MMM-YY', 'MMM YYYY'],
  DATE_PATTERN: 'YYYY-MM-DD',
  TIMESTAMP_FORMAT: 'YYYY-MM-DD HH:mm:ss.SSS',
  ISO_FORMAT: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
};


export const QUICKBOOKS_SERVICE_CONFIG = {
  NAME: 'QuickBooks Service',
  VERSION: 'v1',
  DEFAULT_PORT: 3002,
  DEFAULT_ENV: 'development',
};



export const QUICKBOOKS_CORS_CONFIG = {
  ALLOWED_DOMAIN: 'getondataconsulting.in',
  METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  ALLOWED_HEADERS: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
  ],
  EXPOSED_HEADERS: ['Content-Range', 'X-Content-Range'],
  CREDENTIALS: true,
  MAX_AGE: 86400,
  PREFLIGHT_CONTINUE: false,
  OPTIONS_SUCCESS_STATUS: 204,
};



export const QUICKBOOKS_SESSION_CONFIG = {
  NAME: 'quickbooks_session_cookie',
  COOKIE: {
    SECURE: process.env.NODE_ENV === 'production',
    HTTP_ONLY: true,
    SAME_SITE: 'strict',
    MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours
  },
};



export const QUICKBOOKS_REQUEST_CONFIG = {
  JSON_LIMIT: '10kb',
  URL_ENCODED_LIMIT: '10kb',
};



export const QUICKBOOKS_DEFAULT_VALUES = {
  PRODUCTION: 'production',
  DEVELOPMENT: 'development',
  NO_ORIGIN: 'No origin',
  BATCH_SIZE: 1000,
  REQUEST_TIMEOUT_MS: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,
  TOKEN_EXPIRY_MS: 3600000, // 1 hour
  POOL_MIN: 2,
  POOL_MAX: 10,
  POOL_ACQUIRE_TIMEOUT: 60000,
  POOL_IDLE_TIMEOUT: 300000,
  POOL_CREATE_TIMEOUT: 30000,
  POOL_DESTROY_TIMEOUT: 5000,
  POOL_REAP_INTERVAL: 1000,
  POOL_RETRY_INTERVAL: 200,
  DATABASE_NAME_PREFIX: 'QB_',
  DATABASE_NAME_SUFFIX: '_DB',
  UPLOAD_DIRECTORY_PREFIX: 'uploads/quickbooks/',
  DEFAULT_UPLOAD_DIR: 'uploads/quickbooks',
  GRANT_TYPE_AUTH_CODE: 'authorization_code',
  GRANT_TYPE_REFRESH: 'refresh_token',
  CONTENT_TYPE_FORM: 'application/x-www-form-urlencoded',
  AUTH_BASIC_PREFIX: 'Basic ',
  ACCEPT: 'application/json',
};



export const QUICKBOOKS_FIELD_NAMES = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  COMPANY_NAME: 'company_name',
  EMAIL: 'email',
  UPDATED_AT: 'updated_at',
  ORGANIZATION_ID: 'organization_id',
  REALM_ID: 'realm_id',
  IS_DELETED: 'is_deleted',
  QUICKBOOK_ACCOUNT_ID: 'quickbook_account_id',
  TOKEN_EXPIRY_TIME: 'token_expiry_time',
  ARITEK_ID: 'aritek_id',
  USER_ID: 'user_id',
  STATUS: 'status',
  LAST_SYNCED: 'last_synced',
};



export const QUICKBOOKS_VALIDATION = {
  REQUIRED_FIELDS: {
    ADD_ACCOUNT: ['code', 'organization_id'],
    ADD_TOKEN: ['id', 'refresh_token', 'access_token'],
    GET_TOKENS: ['id'],
    SYNC_DATA: ['organization_id', 'id'],
  },
  FIELD_LENGTHS: {
    MAX_TEXT_LENGTH: 4000,
    MAX_STRING_LENGTH: 255,
  },
};



export const QUICKBOOKS_STATUS = {
  AUTOMATIC: 'automatic',
  COMPLETED: 'completed',
  FAILED: 'failed',
};



export const QUICKBOOKS_HTTP_HEADERS = {
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
};



export const QUICKBOOKS_DATABASE = {
  FALLBACK: {
    SERVER: 'localhost',
    USER: 'sa',
    PASSWORD: 'password',
  },
  QB_DB_CONNECTION_CLOSED: 'Connection is closed',
  QB_DB_CONNECTION_TIMEOUT: 'Connection timeout',
  ERROR_TYPES: {
    TIMEOUT: 'timeout',
    ETIMEOUT: 'ETIMEOUT',
    ECONNRESET: 'ECONNRESET',
  },
};



export const QUICKBOOKS_TABLE_NAMES = [
  'Account',
  'Customer',
  'Vendor',
  'Item',
  'Invoice',
  'Payment',
  'Bill',
  'JournalEntry',
  'Purchase',
  'SalesReceipt',
];



export const QUICKBOOKS_TABLE_CONFIG = {
  Account: {
    sqlTableName: 'QB_Accounts',
    primaryKey: 'Id',
  },
  Customer: {
    sqlTableName: 'QB_Customers',
    primaryKey: 'Id',
  },
  Vendor: {
    sqlTableName: 'QB_Vendors',
    primaryKey: 'Id',
  },
  Item: {
    sqlTableName: 'QB_Items',
    primaryKey: 'Id',
  },
  Invoice: {
    sqlTableName: 'QB_Invoices',
    primaryKey: 'Id',
  },
  Payment: {
    sqlTableName: 'QB_Payments',
    primaryKey: 'Id',
  },
  Bill: {
    sqlTableName: 'QB_Bills',
    primaryKey: 'Id',
  },
  JournalEntry: {
    sqlTableName: 'QB_JournalEntries',
    primaryKey: 'Id',
  },
  Purchase: {
    sqlTableName: 'QB_Purchases',
    primaryKey: 'Id',
  },
  SalesReceipt: {
    sqlTableName: 'QB_SalesReceipts',
    primaryKey: 'Id',
  },
};



export const QUICKBOOKS_LINE_ITEM_MAPPING = {
  'LineItem': 'Item',
  'Line': 'Item',
};



export const QUICKBOOKS_DEFAULTS = {
  SQL_ERROR_FOREIGN_KEY: 547,
  SQL_ERROR_DUPLICATE: 2627,
  POSTGRES_UNIQUE_VIOLATION: '23505',
  REQUEST_TIMEOUT_MS: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,
  TOKEN_EXPIRY_MS: 3600000,
  POOL_MIN: 2,
  POOL_MAX: 10,
  POOL_ACQUIRE_TIMEOUT: 60000,
  POOL_IDLE_TIMEOUT: 300000,
  POOL_CREATE_TIMEOUT: 30000,
  POOL_DESTROY_TIMEOUT: 5000,
  POOL_REAP_INTERVAL: 1000,
  POOL_RETRY_INTERVAL: 200,
  BATCH_SIZE: 1000,
  DATABASE_NAME_PREFIX: 'QB_',
  DATABASE_NAME_SUFFIX: '_DB',
  UPLOAD_DIRECTORY_PREFIX: 'uploads/quickbooks/',
  DEFAULT_UPLOAD_DIR: 'uploads/quickbooks',
  GRANT_TYPE_AUTH_CODE: 'authorization_code',
  GRANT_TYPE_REFRESH: 'refresh_token',
  CONTENT_TYPE_FORM: 'application/x-www-form-urlencoded',
  AUTH_BASIC_PREFIX: 'Basic ',
};



export const QUICKBOOKS_PROCESS_SIGNALS = {
  SIGTERM: 'SIGTERM',
  SIGINT: 'SIGINT',
};



export const QUICKBOOKS_SERVER_ERROR_CODES = {
  EADDRINUSE: 'EADDRINUSE',
};



export const QUICKBOOKS_AVAILABLE_ENDPOINTS = [
  '/api/v1/quickbooks',
  '/api/v1/reports',
];



export const QUICKBOOKS_LOGGER_NAMES = {
  AWS_CONFIG: 'AWS_CONFIG',
  QUICKBOOKS_CONTROLLER: 'QUICKBOOKS_CONTROLLER',
  QUICKBOOKS_SERVICE: 'QUICKBOOKS_SERVICE',
  QUICKBOOKS_REPOSITORY: 'QUICKBOOKS_REPOSITORY',
  QUICKBOOKS_MIDDLEWARE: 'QUICKBOOKS_MIDDLEWARE',
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1,
};



export const QUICKBOOKS_SEQUELIZE_OPERATORS = {
  NE: 'ne',
  IN: 'in',
  GTE: 'gte',
  LTE: 'lte',
};



export const QUICKBOOKS_PRIMARY_KEYS = {
  DB_FIELDS: {
    ID: 'id',
  },
};



export const TIME_CONSTANTS = {
  MILLISECONDS_PER_MINUTE: 60 * 1000,
  FIFTEEN_MINUTES: 15 * 60 * 1000,
  SEVEN_DAYS: 7 * 24 * 60 * 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  COOKIE_AGE: 24 * 60 * 60 * 1000,
  TOKEN_EXPIRY_HOURS: 1,
  TOKEN_EXPIRY_MS: 3600000, // 1 hour in milliseconds
};



export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
  OPTIONS: 'OPTIONS',
  HEAD: 'HEAD',
};

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};



export const COOKIE_CONFIG = {
  HTTP_ONLY: true,
  SECURE: process.env.NODE_ENV !== 'development',
  SAME_SITE: process.env.NODE_ENV === 'development' ? 'lax' : 'strict',
  MAX_AGE: parseInt(process.env.COOKIE_MAX_AGE || '3600000'),
  PATH: '/',
  DOMAIN: process.env.COOKIE_DOMAIN,
  STRICT: 'strict',
  ACCESS_MAX_AGE: 15 * 60 * 1000,
  REFRESH_MAX_AGE: 7 * 24 * 60 * 60 * 1000,
};



export const SECURITY_PASSWORD_CONFIG = {
  MIN_LENGTH: 8,
  MAX_LENGTH: 30,
  SALT_ROUNDS: 8,
  PREVIOUS_PASSWORDS: 3,
};

export const SECURITY_RATE_LIMIT = {
  WINDOW_MS: 15 * 60 * 1000,
  MAX_REQUESTS: 100,
  MESSAGE: 'Too many requests, please try again later.',
};



export const TOKEN_CONFIG = {
  ACCESS: { KEY: 'accessToken', TYPE: 'access' },
  REFRESH: { KEY: 'refreshToken', TYPE: 'refresh' },
  AUDIT_TYPE: 'tokenRefresh',
  SERVICE_NAME: 'token-refresh-service',
};



export const AUDIT_EVENTS = {
  LOGIN: 'login',
  LOGOUT: 'logout',
  TOKEN_REFRESH: 'tokenRefresh',
  FAILED_LOGIN: 'failedLogin',
  PASSWORD_CHANGE: 'passwordChange',
};

export const AUDIT_SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
};

export const AUDIT_CONFIG = {
  RETENTION_DAYS: 30,
};



export const DEFAULT_VALUES = {
  PORT: '3003',
  NODE_ENV: 'development',
  DB_DIALECT: 'postgres',
  DB_PORT: '5432',
  SESSION_SECRET: 'your_secret_key',
};



export const DATA_TYPES = {
  OBJECT: 'object',
  STRING: 'string',
  FUNCTION: 'function',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
};



export const BOOLEAN_VALUES = {
  TRUE: true,
  FALSE: false,
};



export const HEALTH_CHECK_ENDPOINT = '/health';

export default {
  CONFIG_DEFAULTS,
  CONFIG_ENV_TYPES,
  CONFIG_VALIDATION,
  ENCRYPTION_MESSAGES,
  DATE_FORMATS,
  TIME_CONSTANTS,
  ENV_TYPES,
  HTTP_METHODS,
  HTTP_STATUS,
  COOKIE_CONFIG,
  SECURITY_PASSWORD_CONFIG,
  SECURITY_RATE_LIMIT,
  TOKEN_CONFIG,
  AUDIT_EVENTS,
  AUDIT_SEVERITY,
  AUDIT_CONFIG,
  DEFAULT_VALUES,
  DATA_TYPES,
  BOOLEAN_VALUES,
  HEALTH_CHECK_ENDPOINT,
  PAGINATION,
};
