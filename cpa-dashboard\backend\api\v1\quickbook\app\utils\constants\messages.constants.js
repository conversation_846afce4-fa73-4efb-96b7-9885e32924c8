export const LOGGER_NAMES = {
  DATE_UTILS: 'DATE_UTILS',
  DATABASE_UTILS: 'DATABASE_UTILS',
  JWT_UTILS: 'JWT_UTILS',
  ERROR_UTILS: 'ERROR_UTILS',
  ENV_VALIDATOR: 'ENV_VALIDATOR',
  SENDGRID_EMAIL_UTILS: 'SENDGRID_EMAIL_UTILS',
};

export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
};

export const LOG_FORMATS = {
  COMBINED: 'combined',
  COMMON: 'common',
  DEV: 'dev',
  SHORT: 'short',
  TINY: 'tiny',
};

export const RESPONSE_MESSAGES = {
  DATA_RETRIEVED_SUCCESS: 'Data retrieved successfully',
  VALIDATION_FAILED: 'Validation failed',
};

export const DATABASE_REPOSITORY_LOGS = {
  RECORD_CREATED: (modelName, id) => `Record created in ${modelName} with ID: ${id}`,
  CREATE_ERROR: (modelName, error) => `Error creating record in ${modelName}: ${error}`,
  RECORD_FOUND: (modelName, id) => `Record found in ${modelName} with ID: ${id}`,
  RECORD_NOT_FOUND: (modelName, id) => `Record not found in ${modelName} with ID: ${id}`,
  FIND_ERROR: (modelName, id, error) => `Error finding record in ${modelName} with ID ${id}: ${error}`,
  RECORD_UPDATED: (modelName, id) => `Record updated in ${modelName} with ID: ${id}`,
  NO_RECORD_TO_UPDATE: (modelName, id) => `No record to update in ${modelName} with ID: ${id}`,
  UPDATE_ERROR: (modelName, id, error) => `Error updating record in ${modelName} with ID ${id}: ${error}`,
  RECORD_SOFT_DELETED: (modelName, id) => `Record soft deleted in ${modelName} with ID: ${id}`,
  NO_RECORD_TO_DELETE: (modelName, id) => `No record to delete in ${modelName} with ID: ${id}`,
  DELETE_ERROR: (modelName, id, error) => `Error deleting record in ${modelName} with ID ${id}: ${error}`,
  RECORDS_FOUND: (modelName, count) => `Found ${count} records in ${modelName}`,
  FIND_ALL_ERROR: (modelName, error) => `Error finding all records in ${modelName}: ${error}`,
  RECORDS_PAGINATED: (modelName, page, limit, total) => `Paginated ${modelName}: page ${page}, limit ${limit}, total ${total}`,
  PAGINATION_ERROR: (modelName, error) => `Error paginating records in ${modelName}: ${error}`,
  RECORD_EXISTS_CHECK: (modelName, id, exists) => `Record exists check in ${modelName} with ID ${id}: ${exists}`,
  EXISTS_CHECK_ERROR: (modelName, id, error) => `Error checking if record exists in ${modelName} with ID ${id}: ${error}`,
  RECORDS_COUNTED: (modelName, count) => `Counted ${count} records in ${modelName}`,
  COUNT_ERROR: (modelName, error) => `Error counting records in ${modelName}: ${error}`,
};

export default {
  LOGGER_NAMES,
  LOG_LEVELS,
  LOG_FORMATS,
  RESPONSE_MESSAGES,
  DATABASE_REPOSITORY_LOGS,
};
