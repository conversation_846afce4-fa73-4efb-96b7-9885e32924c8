
export const HARDCODED_STRINGS = {
  MISSING_ENV_VARS: 'Missing environment variables',
  MISSING_REQUIRED_ENV_VARS: 'Missing required environment variables',
  NODE_ENV_DEVELOPMENT: 'development',
  NODE_ENV_PRODUCTION: 'production',
  
  SELECT_1: 'SELECT 1',
  MSSQL_CLIENT: 'mssql',
  BASE64_ENCODING: 'base64',
  UTF8_ENCODING: 'utf8',
  QUERY_ERROR_EVENT: 'query-error',
  
  CONTENT_TYPE_FORM: 'application/x-www-form-urlencoded',
  BEARER_PREFIX: 'Bearer ',
  BASIC_AUTH_PREFIX: 'Basic ',
  
  CSV_EXTENSION: '.csv',
  SINGLE_QUOTE_REPLACEMENT: "''",
  
  ORGANIZATION_ID_AND_ACCOUNT_ID_REQUIRED: 'Organization ID and Account ID are required',
  FAILED_TO_DECRYPT_REFRESH_TOKEN: 'Failed to decrypt refresh token',
  UNKNOWN_USER: 'unknown',
  
  TRIAL_BALANCE: 'TrialBalance',
  PROFIT_AND_LOSS: 'ProfitAndLoss',
  BALANCE_SHEET: 'BalanceSheet',
  TRIAL_BALANCE_DISPLAY: 'Trial Balance',
  PROFIT_LOSS_DISPLAY: 'Profit & Loss',
  BALANCE_SHEET_DISPLAY: 'Balance Sheet',
  
  REALM_ID: 'realmId',
  ACCESS_TOKEN: 'accessToken',
  DATE_RANGE: 'dateRange',
  CODE: 'code',
  TRUE: 'true',
  FALSE: 'false',
  
  CREATED_AT: 'created_at',
  DESC: 'DESC',
  
  ENV_VARS: {
    NODE_ENV: 'NODE_ENV',
    QUICKBOOKS_SERVICE_PORT: 'QUICKBOOKS_SERVICE_PORT',
    SERVER_URL: 'SERVER_URL',
    FRONTEND_URL: 'FRONTEND_URL',
    DB_HOST: 'DB_HOST',
    DB_NAME: 'DB_NAME',
    DB_USER: 'DB_USER',
    DB_PASS: 'DB_PASS',
    JWT_ACCESS_SECRET: 'JWT_ACCESS_SECRET',
    QUICKBOOKS_CLIENT_ID: 'QUICKBOOKS_CLIENT_ID',
    QUICKBOOKS_CLIENT_SECRET: 'QUICKBOOKS_CLIENT_SECRET',
    QUICKBOOKS_TOKEN_URL: 'QUICKBOOKS_TOKEN_URL',
    SANDBOX_URL: 'SANDBOX_URL',
    REDIRECT_URI: 'REDIRECT_URI',
    DB_SERVER: 'DB_SERVER',
    DB_USER: 'DB_USER',
    DB_PASSWORD: 'DB_PASSWORD'
  },
  
  REQUIRED_ENV_VARS: [
    'clientID',
    'clientSecret', 
    'tokenUrl',
    'baseUrl'
  ],
  
  DB_CONFIG: {
    CLIENT: 'mssql',
    ENCRYPT: true,
    TRUST_SERVER_CERTIFICATE: true,
    ENABLE_ARITH_ABORT: true
  },
  
  FILE_SYSTEM: {
    UTF8: 'utf8',
    CSV_EXT: '.csv'
  },
  
  STRING_OPS: {
    SINGLE_QUOTE_REPLACEMENT: "''",
    BASE64: 'base64'
  },
  
  HTTP_HEADERS: {
    CONTENT_TYPE_FORM: 'application/x-www-form-urlencoded',
    BEARER: 'Bearer ',
    BASIC: 'Basic '
  },
  
  VALIDATION_FIELDS: {
    REALM_ID: 'realmId',
    ACCESS_TOKEN: 'accessToken', 
    DATE_RANGE: 'dateRange',
    QUICKBOOK_ACCESS_TOKEN: 'quickBookAccesstoken'
  },
  
  REPORT_TYPES: {
    TRIAL_BALANCE: 'TrialBalance',
    PROFIT_AND_LOSS: 'ProfitAndLoss',
    BALANCE_SHEET: 'BalanceSheet',
    TRIAL_BALANCE_DISPLAY: 'Trial Balance',
    PROFIT_LOSS_DISPLAY: 'Profit & Loss',
    BALANCE_SHEET_DISPLAY: 'Balance Sheet'
  },
  
  BOOLEAN: {
    TRUE: 'true',
    FALSE: 'false'
  },
  
  DB_ORDER: {
    CREATED_AT_DESC: [['created_at', 'DESC']]
  },
  
  REPORT_DEFAULTS: {
    COL_TYPE: 'Money',
    REPORT_NAME_TRIAL_BALANCE: 'TrialBalance',
    REPORT_NAME_PROFIT_LOSS: 'ProfitAndLoss',
    REPORT_NAME_BALANCE_SHEET: 'BalanceSheet',
    REPORT_BASIS: 'Accrual',
    SUMMARIZE_COLUMNS_BY: 'Month',
    CURRENCY: 'USD',
    ROW_TYPE_DATA: 'Data',
    PERIOD_DATES: 'Period dates',
    FAILED_TO_REFRESH_TOKEN: 'Failed to refresh token'
  },
  
  QUICKBOOK_ACCOUNT_MESSAGES: {
    TOKEN_REQUIRED: 'Token is required',
    FETCH_AND_STORE_STARTED: 'Fetching and storing QuickBooks account info...',
    API_URL_NOT_CONFIGURED: 'QuickBooks account API URL not configured',
    FETCH_AND_STORE_SUCCESS: 'Account info fetched and stored successfully',
    ERROR_MESSAGE: 'Error in fetchAndStoreQuickbookAccount:'
  }
};

export const ENCRYPTION_STRINGS = {
  UTF8: 'utf8',
  HEX: 'hex',
  SHA256: 'sha256',
  BASE64URL: 'base64url'
};

export const TIME_STRINGS = {
  MINUTES: 'minutes',
  MINUTE: 'minute',
  HOURS: 'hours',
  HOUR: 'hour',
  DAYS: 'days',
  DAY: 'day',
  AGO: 'ago'
};

export default HARDCODED_STRINGS;
