import { createLogger } from './logger.utils.js';
import { DATE_ERROR_MESSAGES } from './constants/error.constants.js';
import { LOGGER_NAMES } from './constants/messages.constants.js';
import { DATE_FORMAT_PATTERNS } from './constants/config.constants.js';
import { TIME_STRINGS } from './constants/strings.constants.js';

const _logger = createLogger(LOGGER_NAMES.DATE_UTILS);

/**
 * Format date to specific format
 * @param {Date|string} date - Date to format
 * @param {string} format - Format string
 * @returns {string} Formatted date
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE);
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return format
    .replace(DATE_FORMAT_PATTERNS.YEAR, year)
    .replace(DATE_FORMAT_PATTERNS.MONTH, month)
    .replace(DATE_FORMAT_PATTERNS.DAY, day)
    .replace(DATE_FORMAT_PATTERNS.HOURS, hours)
    .replace(DATE_FORMAT_PATTERNS.MINUTES, minutes)
    .replace(DATE_FORMAT_PATTERNS.SECONDS, seconds);
};

/**
 * Parse date string to Date object
 * @param {string} dateString - Date string to parse
 * @returns {Date|null} Parsed date or null if invalid
 */
export const parseDate = (dateString) => {
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
};

/**
 * Check if date is valid
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is valid
 */
export const isValidDate = (date) => {
  const dateObj = new Date(date);
  return !isNaN(dateObj.getTime());
};

/**
 * Get date difference in days
 * @param {Date|string} date1 - First date
 * @param {Date|string} date2 - Second date
 * @returns {number} Difference in days
 */
export const getDateDifferenceInDays = (date1, date2) => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  if (!isValidDate(d1) || !isValidDate(d2)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }

  const diffTime = Math.abs(d2 - d1);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

/**
 * Add days to date
 * @param {Date|string} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date
 */
export const addDays = (date, days) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
};

/**
 * Subtract days from date
 * @param {Date|string} date - Base date
 * @param {number} days - Number of days to subtract
 * @returns {Date} New date
 */
export const subtractDays = (date, days) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setDate(dateObj.getDate() - days);
  return dateObj;
};

/**
 * Get start of day (00:00:00)
 * @param {Date|string} date - Date to get start of day for
 * @returns {Date} Start of day
 */
export const getStartOfDay = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
};

/**
 * Get end of day (23:59:59.999)
 * @param {Date|string} date - Date to get end of day for
 * @returns {Date} End of day
 */
export const getEndOfDay = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
};

/**
 * Get start of week (Monday)
 * @param {Date|string} date - Date to get start of week for
 * @returns {Date} Start of week
 */
export const getStartOfWeek = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  const day = dateObj.getDay();
  const diff = dateObj.getDate() - day + (day === 0 ? -6 : 1);
  dateObj.setDate(diff);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
};

/**
 * Get end of week (Sunday)
 * @param {Date|string} date - Date to get end of week for
 * @returns {Date} End of week
 */
export const getEndOfWeek = (date) => {
  const startOfWeek = getStartOfWeek(date);
  startOfWeek.setDate(startOfWeek.getDate() + 6);
  startOfWeek.setHours(23, 59, 59, 999);
  return startOfWeek;
};

/**
 * Get start of month
 * @param {Date|string} date - Date to get start of month for
 * @returns {Date} Start of month
 */
export const getStartOfMonth = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setDate(1);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
};

/**
 * Get end of month
 * @param {Date|string} date - Date to get end of month for
 * @returns {Date} End of month
 */
export const getEndOfMonth = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  dateObj.setMonth(dateObj.getMonth() + 1, 0);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
};

/**
 * Get last month's date range (start and end dates)
 * @param {Date|string} [baseDate] - Base date to calculate from (defaults to current date)
 * @returns {Object} Object with startDate and endDate of last month in YYYY-MM-DD format
 */
export const getLastMonthDateRange = (baseDate = new Date()) => {
  try {
    const currentDate = new Date(baseDate);
    if (!isValidDate(currentDate)) {
      throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
    }

    // Get the first day of current month, then subtract 1 day to get last day of previous month
    const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    lastDayOfPreviousMonth.setDate(0); // This sets it to the last day of previous month

    // Get the first day of previous month
    const firstDayOfPreviousMonth = new Date(lastDayOfPreviousMonth.getFullYear(), lastDayOfPreviousMonth.getMonth(), 1);

    // Format dates as YYYY-MM-DD
    const startDate = formatDateOnly(firstDayOfPreviousMonth);
    const endDate = formatDateOnly(lastDayOfPreviousMonth);

    _logger.info(`Generated last month date range: ${startDate} to ${endDate}`);

    return {
      startDate,
      endDate,
      startDateObj: firstDayOfPreviousMonth,
      endDateObj: lastDayOfPreviousMonth
    };
  } catch (error) {
    _logger.error('Error generating last month date range:', error.message);
    throw error;
  }
};

/**
 * Check if date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is today
 */
export const isToday = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    return false;
  }
  
  const today = new Date();
  return dateObj.toDateString() === today.toDateString();
};

/**
 * Check if date is in the past
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the past
 */
export const isPast = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    return false;
  }
  
  const now = new Date();
  return dateObj < now;
};

/**
 * Check if date is in the future
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the future
 */
export const isFuture = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    return false;
  }
  
  const now = new Date();
  return dateObj > now;
};

/**
 * Get age from birth date
 * @param {Date|string} birthDate - Birth date
 * @returns {number} Age in years
 */
export const getAge = (birthDate) => {
  const birth = new Date(birthDate);
  if (!isValidDate(birth)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_BIRTH_DATE);
  }
  
  const today = new Date();
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 * @param {Date|string} date - Date to format
 * @returns {string} Relative time string
 */
export const formatRelativeTime = (date) => {
  const dateObj = new Date(date);
  if (!isValidDate(dateObj)) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE_PROVIDED);
  }
  
  const now = new Date();
  const diffMs = now - dateObj;
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return 'just now';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} ${diffMinutes !== 1 ? TIME_STRINGS.MINUTES : TIME_STRINGS.MINUTE} ${TIME_STRINGS.AGO}`;
  } else if (diffHours < 24) {
    return `${diffHours} ${diffHours !== 1 ? TIME_STRINGS.HOURS : TIME_STRINGS.HOUR} ${TIME_STRINGS.AGO}`;
  } else if (diffDays < 7) {
    return `${diffDays} ${diffDays !== 1 ? TIME_STRINGS.DAYS : TIME_STRINGS.DAY} ${TIME_STRINGS.AGO}`;
  } else {
    return formatDate(dateObj, 'MMM DD, YYYY');
  }
};

/**
 * Get current timestamp
 * @returns {number} Current timestamp in milliseconds
 */
export const getCurrentTimestamp = () => {
  return Date.now();
};

/**
 * Get current date in ISO format
 * @returns {string} Current date in ISO format
 */
export const getCurrentDateISO = () => {
  return new Date().toISOString();
};

/**
 * Get current date in local format
 * @returns {string} Current date in local format
 */
export const getCurrentDateLocal = () => {
  return new Date().toLocaleDateString();
};

/**
 * Format date to show only the date part (YYYY-MM-DD)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDateOnly = (date) => {
  if (!date) return null;
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_DATE);
  }
  
  return formatDate(dateObj, 'YYYY-MM-DD');
};

/**
 * Create a validated date range from start and end dates
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @returns {Object} Object with startDate and endDate
 */
export const createValidatedDateRange = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime())) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_START_DATE);
  }
  
  if (isNaN(end.getTime())) {
    throw new Error(DATE_ERROR_MESSAGES.INVALID_END_DATE);
  }
  
  if (start > end) {
    throw new Error(DATE_ERROR_MESSAGES.START_DATE_AFTER_END_DATE);
  }
  
  return {
    startDate: start,
    endDate: end
  };
};

export default {
  formatDate,
  parseDate,
  isValidDate,
  getDateDifferenceInDays,
  addDays,
  subtractDays,
  getStartOfDay,
  getEndOfDay,
  getStartOfWeek,
  getEndOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  getLastMonthDateRange,
  isToday,
  isPast,
  isFuture,
  getAge,
  formatRelativeTime,
  getCurrentTimestamp,
  getCurrentDateISO,
  getCurrentDateLocal,
};
