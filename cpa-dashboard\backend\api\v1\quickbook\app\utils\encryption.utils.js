import crypto from 'crypto';
import { ENCRYPTION_STRINGS } from './constants/strings.constants.js';
import { ENCRYPTION_DEFAULTS } from './constants/config.constants.js';
import { ENCRYPTION_ERROR_MESSAGES } from './constants/error.constants.js';

/**
 * Generate a random encryption key
 * @param {number} length - Length of the key in bytes
 * @returns {string} Generated encryption key
 */
export const generateEncryptionKey = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate a random initialization vector
 * @param {number} length - Length of the IV in bytes
 * @returns {string} Generated IV
 */
export const generateIV = (length = 16) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Encrypt data using AES-256-GCM
 * @param {string} data - Data to encrypt
 * @param {string} key - Encryption key
 * @param {string} iv - Initialization vector
 * @returns {Object} Encrypted data with auth tag
 */
export const encryptData = (data, key, _iv) => {
  try {
    const cipher = crypto.createCipher(ENCRYPTION_DEFAULTS.ALGORITHM, key);
    let encrypted = cipher.update(data, ENCRYPTION_STRINGS.UTF8, ENCRYPTION_STRINGS.HEX);
    encrypted += cipher.final(ENCRYPTION_STRINGS.HEX);
    
    return {
      encrypted,
      authTag: cipher.getAuthTag().toString(ENCRYPTION_STRINGS.HEX)
    };
  } catch {
    throw new Error(ENCRYPTION_ERROR_MESSAGES.ENCRYPTION_FAILED);
  }
};

/**
 * Decrypt data using AES-256-GCM
 * @param {string} encryptedData - Encrypted data
 * @param {string} key - Decryption key
 * @param {string} iv - Initialization vector
 * @param {string} authTag - Authentication tag
 * @returns {string} Decrypted data
 */
export const decryptData = (encryptedData, key, iv, authTag) => {
  try {
    const decipher = crypto.createDecipher(ENCRYPTION_DEFAULTS.ALGORITHM, key);
    decipher.setAuthTag(Buffer.from(authTag, ENCRYPTION_STRINGS.HEX));
    
    let decrypted = decipher.update(encryptedData, ENCRYPTION_STRINGS.HEX, ENCRYPTION_STRINGS.UTF8);
    decrypted += decipher.final(ENCRYPTION_STRINGS.UTF8);
    
    return decrypted;
  } catch {
    throw new Error(ENCRYPTION_ERROR_MESSAGES.DECRYPTION_FAILED);
  }
};

/**
 * Hash data using SHA-256
 * @param {string} data - Data to hash
 * @returns {string} Hashed data
 */
export const hashData = (data) => {
  return crypto.createHash(ENCRYPTION_STRINGS.SHA256).update(data).digest(ENCRYPTION_STRINGS.HEX);
};

/**
 * Generate HMAC signature
 * @param {string} data - Data to sign
 * @param {string} secret - Secret key
 * @returns {string} HMAC signature
 */
export const generateHMAC = (data, secret) => {
  return crypto.createHmac(ENCRYPTION_STRINGS.SHA256, secret).update(data).digest(ENCRYPTION_STRINGS.HEX);
};

/**
 * Verify HMAC signature
 * @param {string} data - Original data
 * @param {string} signature - HMAC signature
 * @param {string} secret - Secret key
 * @returns {boolean} True if signature is valid
 */
export const verifyHMAC = (data, signature, secret) => {
  const expectedSignature = generateHMAC(data, secret);
  return crypto.timingSafeEqual(
    Buffer.from(signature, ENCRYPTION_STRINGS.HEX),
    Buffer.from(expectedSignature, ENCRYPTION_STRINGS.HEX)
  );
};

/**
 * Generate secure random string
 * @param {number} length - Length of the string
 * @returns {string} Random string
 */
export const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString(ENCRYPTION_STRINGS.BASE64URL);
};

/**
 * Generate secure random number
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Random number
 */
export const generateRandomNumber = (min = 0, max = 1) => {
  const randomBuffer = crypto.randomBytes(4);
  const randomValue = randomBuffer.readUInt32BE(0);
  return min + (randomValue / (0xffffffff + 1)) * (max - min);
};

/**
 * Simple encrypt function for backward compatibility
 * @param {string} data - Data to encrypt
 * @returns {Promise<string>} Encrypted data
 */
export const encrypt = async (data) => {
  try {
    const key = process.env.ENCRYPTION_KEY ? process.env.ENCRYPTION_KEY : generateEncryptionKey();
    const result = encryptData(data, key);
    return result.encrypted;
  } catch {
    throw new Error(ENCRYPTION_ERROR_MESSAGES.ENCRYPTION_FAILED);
  }
};

/**
 * Simple decrypt function for backward compatibility
 * @param {string} encryptedData - Encrypted data
 * @returns {Promise<string>} Decrypted data
 */
export const decrypt = async (encryptedData) => {
  try {
    const key = process.env.ENCRYPTION_KEY ? process.env.ENCRYPTION_KEY : generateEncryptionKey();
    // For simple decryption, we'll use a basic approach
    const decipher = crypto.createDecipher(ENCRYPTION_DEFAULTS.ALGORITHM, key);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch {
    throw new Error(ENCRYPTION_ERROR_MESSAGES.DECRYPTION_FAILED);
  }
};

export default {
  generateEncryptionKey,
  generateIV,
  encryptData,
  decryptData,
  hashData,
  generateHMAC,
  verifyHMAC,
  generateRandomString,
  generateRandomNumber,
  encrypt,
  decrypt
};
