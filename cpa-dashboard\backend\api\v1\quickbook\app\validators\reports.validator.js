import Joi from 'joi';
import { COMMON_MESSAGES } from '../utils/constants/error.constants.js';

export const trialBalanceSchema = Joi.object({
  startDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'startDate must be in YYYY-MM-DD format',
      'any.required': 'startDate is required'
    }),
  endDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'endDate must be in YYYY-MM-DD format',
      'any.required': 'endDate is required'
    }),
  quickBookAccesstoken: Joi.string()
    .required()
    .messages({
      'any.required': 'quickBookAccesstoken is required'
    }),
  organizationId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.guid': 'organizationId must be a valid UUID',
      'any.required': 'organizationId is required'
    }),
  companyId: Joi.string()
    .required()
    .messages({
      'any.required': 'companyId is required'
    })
}).custom((value, helpers) => {
  const startDate = new Date(value.startDate);
  const endDate = new Date(value.endDate);
  const today = new Date();
  
  // Check if start date is before end date
  if (startDate >= endDate) {
    return helpers.error('custom.dateRange');
  }
  
  // Check if date range exceeds 1 year
  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays > 365) {
    return helpers.error('custom.dateRangeExceed');
  }
  
  // Check if dates are not in the future
  if (startDate > today || endDate > today) {
    return helpers.error('custom.futureDate');
  }
  
  return value;
}).messages({
  'custom.dateRange': COMMON_MESSAGES.START_DATE_BEFORE_END_DATE,
  'custom.dateRangeExceed': COMMON_MESSAGES.DATE_RANGE_EXCEED_ONE_YEAR,
  'custom.futureDate': COMMON_MESSAGES.DATE_CANNOT_BE_FUTURE
});

export const profitLossSchema = Joi.object({
  startDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'startDate must be in YYYY-MM-DD format',
      'any.required': 'startDate is required'
    }),
  endDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'endDate must be in YYYY-MM-DD format',
      'any.required': 'endDate is required'
    }),
  quickBookAccesstoken: Joi.string()
    .required()
    .messages({
      'any.required': 'quickBookAccesstoken is required'
    }),
  organizationId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.guid': 'organizationId must be a valid UUID',
      'any.required': 'organizationId is required'
    }),
  companyId: Joi.string()
    .required()
    .messages({
      'any.required': 'companyId is required'
    })
}).custom((value, helpers) => {
  const startDate = new Date(value.startDate);
  const endDate = new Date(value.endDate);
  const today = new Date();
  
  // Check if start date is before end date
  if (startDate >= endDate) {
    return helpers.error('custom.dateRange');
  }
  
  // Check if date range exceeds 1 year
  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays > 365) {
    return helpers.error('custom.dateRangeExceed');
  }
  
  // Check if dates are not in the future
  if (startDate > today || endDate > today) {
    return helpers.error('custom.futureDate');
  }
  
  return value;
}).messages({
  'custom.dateRange': COMMON_MESSAGES.START_DATE_BEFORE_END_DATE,
  'custom.dateRangeExceed': COMMON_MESSAGES.DATE_RANGE_EXCEED_ONE_YEAR,
  'custom.futureDate': COMMON_MESSAGES.DATE_CANNOT_BE_FUTURE
});

export const balanceSheetSchema = Joi.object({
  startDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'startDate must be in YYYY-MM-DD format',
      'any.required': 'startDate is required'
    }),
  endDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'endDate must be in YYYY-MM-DD format',
      'any.required': 'endDate is required'
    }),
  quickBookAccesstoken: Joi.string()
    .required()
    .messages({
      'any.required': 'quickBookAccesstoken is required'
    }),
  organizationId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.guid': 'organizationId must be a valid UUID',
      'any.required': 'organizationId is required'
    }),
  companyId: Joi.string()
    .required()
    .messages({
      'any.required': 'companyId is required'
    })
}).custom((value, helpers) => {
  const startDate = new Date(value.startDate);
  const endDate = new Date(value.endDate);
  const today = new Date();
  
  // Check if start date is before end date
  if (startDate >= endDate) {
    return helpers.error('custom.dateRange');
  }
  
  // Check if date range exceeds 1 year
  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays > 365) {
    return helpers.error('custom.dateRangeExceed');
  }
  
  // Check if dates are not in the future
  if (startDate > today || endDate > today) {
    return helpers.error('custom.futureDate');
  }
  
  return value;
}).messages({
  'custom.dateRange': COMMON_MESSAGES.START_DATE_BEFORE_END_DATE,
  'custom.dateRangeExceed': COMMON_MESSAGES.DATE_RANGE_EXCEED_ONE_YEAR,
  'custom.futureDate': COMMON_MESSAGES.DATE_CANNOT_BE_FUTURE
});

// General Report Schema (for common validation)
export const generalReportSchema = Joi.object({
  startDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'startDate must be in YYYY-MM-DD format',
      'any.required': 'startDate is required'
    }),
  endDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .messages({
      'string.pattern.base': 'endDate must be in YYYY-MM-DD format',
      'any.required': 'endDate is required'
    }),
  quickBookAccesstoken: Joi.string()
    .required()
    .messages({
      'any.required': 'quickBookAccesstoken is required'
    }),
  organizationId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.guid': 'organizationId must be a valid UUID',
      'any.required': 'organizationId is required'
    }),
  companyId: Joi.string()
    .required()
    .messages({
      'any.required': 'companyId is required'
    })
}).custom((value, helpers) => {
  const startDate = new Date(value.startDate);
  const endDate = new Date(value.endDate);
  const today = new Date();
  
  // Check if start date is before end date
  if (startDate >= endDate) {
    return helpers.error('custom.dateRange');
  }
  
  // Check if date range exceeds 1 year
  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  if (diffDays > 365) {
    return helpers.error('custom.dateRangeExceed');
  }
  
  // Check if dates are not in the future
  if (startDate > today || endDate > today) {
    return helpers.error('custom.futureDate');
  }
  
  return value;
}).messages({
  'custom.dateRange': COMMON_MESSAGES.START_DATE_BEFORE_END_DATE,
  'custom.dateRangeExceed': COMMON_MESSAGES.DATE_RANGE_EXCEED_ONE_YEAR,
  'custom.futureDate': COMMON_MESSAGES.DATE_CANNOT_BE_FUTURE
});

// Export all schemas
export const reportSchemas = {
  trialBalance: trialBalanceSchema,
  profitLoss: profitLossSchema,
  balanceSheet: balanceSheetSchema,
  general: generalReportSchema
};

export default reportSchemas;
