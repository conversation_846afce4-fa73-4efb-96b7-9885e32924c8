// SIKKA API CONFIGURATION
export const SIKKA_API = {
  BASE_URL: "https://api.sikkasoft.com",
  VERSION: "v4",
  ENDPOINTS: {
    AUTHORIZED_PRACTICES: "/authorized_practices",
    REQUEST_KEY: "/request_key",
  },
  GRANT_TYPES: {
    REQUEST_KEY: "request_key",
  },
};

// METHOD TYPES
export const METHOD_TYPES = {
  GET: "get",
  POST: "post",
  PUT: "put",
  DELETE: "delete",
  PATCH: "patch",
};

// SIKKA MESSAGES
export const SIKKA_MESSAGES = {
  REQUEST_KEY_SUCCESS: "Request key generated successfully",
  REQUEST_KEY_FAILED: "Failed to generate request key",
  AUTHORIZED_PRACTICES_SUCCESS: "Authorized practices fetched successfully",
  NO_AUTHORIZED_PRACTICES: "No authorized practices found",
  MISSING_PRACTICE_CREDENTIALS:
    "Missing office_id or secret_key in practice data",
  INVALID_REQUEST_KEY_RESPONSE: "Invalid response from request key API",
  NETWORK_ERROR: "Network error occurred while calling Sikka API",
  INVALID_CREDENTIALS: "Invalid credentials",
  API_CALL_FAILED: "API call failed with status",
  SIKKA_API_ERROR: "Sikka API Error",
  NETWORK_ERROR_OCCURRED: "Network error occurred while calling Sikka API",
};

// MODEL FIELDS
export const MODEL_FIELDS = {
  APP_ID: "app_id",
  APP_KEY: "app_key",
  OFFICE_ID: "office_id",
  SECRET_KEY: "secret_key",
  REQUEST_KEY: "request_key",
  GRANT_TYPE: "grant_type",
  START_TIME: "start_time",
  END_TIME: "end_time",
  EXPIRES_IN: "expires_in",
  EMAIL: "email",
  ITEMS: "items",
  DATA: "data",
};

// VALIDATION RULES
export const VALIDATION_RULES = {
  APP_ID_MIN_LENGTH: 1,
  APP_ID_MAX_LENGTH: 100,
  APP_KEY_MIN_LENGTH: 1,
  APP_KEY_MAX_LENGTH: 255,
};

// VALIDATION MESSAGES
export const VALIDATION_MESSAGES = {
  APP_ID_REQUIRED: "App ID is required",
  APP_ID_LENGTH: "App ID must be between 1 and 100 characters",
  APP_KEY_REQUIRED: "App Key is required",
  APP_KEY_LENGTH: "App Key must be between 1 and 255 characters",
  VALIDATION_FAILED: "Validation failed",
  VALIDATION_PROCESSING_ERROR: "Error processing validation",
};

// LOGGER CONFIGURATION
export const LOGGER_NAMES = {
  SIKKA_CONTROLLER: "sikka-controller",
  SIKKA_SERVICE: "sikka-service",
  VALIDATION_MIDDLEWARE: "validation-middleware",
};

// LOG ACTIONS
export const LOG_ACTIONS = {
  REQUESTING_KEY: "Requesting API key from Sikka",
  REQUEST_KEY_SUCCESS: "Request key generated successfully",
  REQUEST_KEY_FAILED: "Failed to generate request key",
  FETCHING_PRACTICES: "Fetching authorized practices from Sikka",
  PRACTICES_FETCHED: "Authorized practices fetched successfully",
  PRACTICES_FETCH_FAILED: "Failed to fetch authorized practices",
  REQUEST_KEY_API_FAILED: "Request key API call failed",
};

// MODULES AND OPERATIONS
export const MODULES = {
  SIKKA: "sikka",
};

export const OPERATIONS = {
  REQUEST_KEY: "request_key",
  AUTHORIZED_PRACTICES: "authorized_practices",
};

// CONFIGURATION DEFAULTS
export const CONFIG_DEFAULTS = {
  SIKKA_API_TIMEOUT: 30000, // 30 seconds
  REQUEST_KEY_EXPIRY_HOURS: 24, // 24 hours
};

// VALIDATION DEFAULTS
export const VALIDATION_DEFAULTS = {
  DEFAULT_SOURCE: "body",
  LOG_MESSAGE_FAILED: "Validation failed",
  LOG_MESSAGE_MIDDLEWARE_ERROR: "Validation middleware error",
  LOG_MESSAGE_MULTI_FAILED: "Multi-source validation failed",
  LOG_MESSAGE_MULTI_ERROR: "Multi-source validation middleware error",
};

// BUSINESS LOGIC CONSTANTS
export const BUSINESS_CONSTANTS = {
  SAMPLE_EMAIL: "<EMAIL>",
  HTTP_SUCCESS_STATUS: 200,
  CREDENTIAL_SEPARATOR: ", ",
  STRING_TYPE: "string",
};

// HTTP HEADERS
export const HTTP_HEADERS = {
  APP_ID: "App-Id",
  APP_KEY: "App-Key",
  CONTENT_TYPE: "Content-Type",
  APPLICATION_JSON: "application/json",
};

// ERROR MESSAGES FOR VALIDATION
export const ERROR_MESSAGES = {
  APP_ID_REQUIRED_STRING: "App ID is required and must be a string",
  APP_KEY_REQUIRED_STRING: "App Key is required and must be a string",
  API_CALL_FAILED_FOR: "API call failed for authorized practices",
  REQUEST_KEY_API_FAILED: "Request key API failed",
};

// SERVER CONSTANTS
export const SERVER_CONSTANTS = {
  SIKKA_SERVER_LABEL: "sikka-server",
  DEFAULT_ALLOWED_ORIGIN: "http://localhost:3000",
  HEALTH_ENDPOINT: "/health",
  API_ENDPOINT: "/api",
  CATCH_ALL_ROUTE: "*",
  ENVIRONMENT_DEVELOPMENT: "development",
};

// SERVER MESSAGES
export const SERVER_MESSAGES = {
  SIKKA_SERVICE_HEALTHY: "Sikka service is healthy",
  ROUTE_NOT_FOUND: "Route not found",
  INTERNAL_SERVER_ERROR: "Internal server error",
  SIKKA_SERVICE_STARTED: "Sikka service started on port",
  HEALTH_CHECK_URL: "Health check:",
  API_ENDPOINT_URL: "API endpoint:",
  ENVIRONMENT_INFO: "Environment:",
  SIGTERM_RECEIVED: "SIGTERM received, shutting down gracefully",
  SIGINT_RECEIVED: "SIGINT received, shutting down gracefully",
  PROCESS_TERMINATED: "Process terminated",
  UNHANDLED_ERROR: "Unhandled error:",
};

// REQUEST CONFIGURATION
export const REQUEST_CONFIG = {
  JSON_LIMIT: "10mb",
  URL_ENCODED_LIMIT: "10mb",
};
