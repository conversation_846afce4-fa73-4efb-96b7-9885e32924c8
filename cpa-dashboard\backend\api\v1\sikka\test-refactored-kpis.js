/**
 * Test script to verify refactored KPI implementation
 * This script tests that all KPI endpoints maintain the same functionality
 */

import { createLogger } from "./app/utils/logger.util.js";
import {
  handleKpiRequest,
  fetchAndStoreKpi,
  createKpiRecords,
} from "./app/utils/methods.util.js";
import {
  LOG_ACTIONS,
  SIKKA_API,
  REPOSITORY_CONSTANTS,
} from "./app/utils/constants.util.js";

const logger = createLogger("test-refactored-kpis");

/**
 * Mock functions for testing
 */
const mockGetRequestKey = async (office_id) => {
  return {
    request_key: "mock_request_key_123",
    end_time: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
  };
};

const mockGenerateRequestKey = async () => {
  return {
    get: (field) => "new_mock_request_key_456",
  };
};

const mockRepositoryFunction = async (data) => {
  logger.info(`Mock repository function called with ${data.length} items`);
  return data.map((item, index) => ({ ...item, id: `mock_id_${index}` }));
};

const mockBulkCreate = async (model, data) => {
  logger.info(
    `Mock bulk create called for ${model.name} with ${data.length} items`
  );
  return data.map((item, index) => ({ ...item, id: `mock_id_${index}` }));
};

/**
 * Test the generic KPI utility functions
 */
async function testUtilityFunctions() {
  logger.info("Testing utility functions...");

  try {
    // Test createKpiRecords
    const testData = [{ practice_id: 1, amount: 100 }];
    const mockModel = { name: "TestModel" };

    const result = await createKpiRecords(
      mockModel,
      "TEST_ENTITY",
      testData,
      mockBulkCreate,
      logger
    );

    if (result && result.length === 1) {
      logger.info("✅ createKpiRecords test passed");
    } else {
      logger.error("❌ createKpiRecords test failed");
    }

    // Test fetchAndStoreKpi
    const kpiResult = await fetchAndStoreKpi(
      "test_office_id",
      "2024-01-01",
      "2024-01-31",
      "test_endpoint",
      mockRepositoryFunction,
      mockGetRequestKey,
      mockGenerateRequestKey,
      LOG_ACTIONS.ACCOUNT_RECEIVABLES_SUCCESS,
      LOG_ACTIONS.ACCOUNT_RECEIVABLES_FAILED,
      logger
    );

    if (kpiResult && kpiResult.length === 1) {
      logger.info("✅ fetchAndStoreKpi test passed");
    } else {
      logger.error("❌ fetchAndStoreKpi test failed");
    }

    logger.info("All utility function tests completed");
    return true;
  } catch (error) {
    logger.error("❌ Utility function tests failed:", error.message);
    return false;
  }
}

/**
 * Test controller utility function
 */
async function testControllerUtility() {
  logger.info("Testing controller utility...");

  try {
    // Mock Express request and response objects
    const mockReq = {
      body: {
        office_id: "test_office",
        start_date: "2024-01-01",
        end_date: "2024-01-31",
      },
    };

    const mockRes = {
      status: function (code) {
        this.statusCode = code;
        return this;
      },
      json: function (data) {
        this.responseData = data;
        return this;
      },
    };

    const mockServiceFunction = async (office_id, start_date, end_date) => {
      return [{ id: "test_1", office_id, start_date, end_date }];
    };

    const statusCodes = {
      STATUS_CODE_SUCCESS: 200,
      STATUS_CODE_INTERNAL_SERVER_ERROR: 500,
    };

    const responseUtils = {
      successResponse: (message, data) => ({ success: true, message, data }),
      errorResponse: (message) => ({ success: false, message }),
    };

    await handleKpiRequest(
      mockReq,
      mockRes,
      mockServiceFunction,
      "Test success message",
      "TEST_SUCCESS_LOG",
      "TEST_FAILED_LOG",
      logger,
      statusCodes,
      responseUtils
    );

    if (mockRes.statusCode === 200 && mockRes.responseData.success) {
      logger.info("✅ handleKpiRequest test passed");
      return true;
    } else {
      logger.error("❌ handleKpiRequest test failed");
      return false;
    }
  } catch (error) {
    logger.error("❌ Controller utility test failed:", error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  logger.info("🚀 Starting refactored KPI implementation tests...");

  const utilityTestResult = await testUtilityFunctions();
  const controllerTestResult = await testControllerUtility();

  if (utilityTestResult && controllerTestResult) {
    logger.info(
      "🎉 All tests passed! Refactored implementation is working correctly."
    );
    return true;
  } else {
    logger.error("❌ Some tests failed. Please check the implementation.");
    return false;
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      logger.error("Test execution failed:", error);
      process.exit(1);
    });
}

export { runTests };
