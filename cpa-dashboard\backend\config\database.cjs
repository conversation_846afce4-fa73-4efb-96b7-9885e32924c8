// config/database.cjs - CommonJS format for Sequelize CLI
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

/**
 * Get database configuration based on environment
 * @returns {Object} Database configuration object
 */
function getDatabaseConfig() {
  const isLocal = process.env.USE_LOCAL_DB === 'true';
  
  const config = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    dialect: process.env.DB_DIALECT,
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      ssl: !isLocal ? {
        require: true,
        rejectUnauthorized: false
      } : false
    }
  };

  return config;
}

const dbConfig = getDatabaseConfig();

// Sequelize CLI configuration for migrations and seeders
const cliConfig = {
  development: dbConfig,
  test: dbConfig,
  production: dbConfig,
};

module.exports = cliConfig;
