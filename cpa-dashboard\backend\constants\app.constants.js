  export const SERVER_CONFIG = {
  VERSION: 'v1',
  SERVICE_NAME: 'CPA Dashboard API Gateway',
};

export const SERVICE_URLS = {
  AUTH: 'http://localhost:3001',
  QUICKBOOKS: 'http://localhost:3004',
};

export const API_ENDPOINTS = {
  ROOT: '/',
  HEALTH: '/health',
  AUTH: '/api/v1/auth',
  QUICKBOOKS: '/api/v1/quickbooks',
  REPORTS: '/api/v1/reports',
};

export const CORS_CONFIG = {
  ALLOWED_DOMAINS: [
    process.env.FRONTEND_URL,
    process.env.SERVER_URL,
    process.env.NEXT_PUBLIC_API_URL,
    process.env.PRODUCTION_DOMAIN_HTTPS,
    process.env.PRODUCTION_DOMAIN_HTTP,
  ].filter(Boolean),
  ALLOWED_DOMAIN_PATTERN: 'getondataconsulting.in',
  METHODS: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  ALLOWED_HEADERS: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  EXPOSED_HEADERS: ['Content-Range', 'X-Content-Range'],
  CREDENTIALS: true,
  MAX_AGE: 86400,
};

export const SESSION_CONFIG = {
  NAME: 'session_cookie',
  SECURE: process.env.NODE_ENV === 'production',
  HTTP_ONLY: true,
  SAME_SITE: 'strict',
  MAX_AGE: 24 * 60 * 60 * 1000,
};

export const REQUEST_CONFIG = {
  JSON_LIMIT: '10mb',
  URL_ENCODED_LIMIT: '10mb',
  TIMEOUT: 30000,
};

export const HEALTH_CHECK_CONFIG = {
  SERVICES: [
    { name: 'Auth Service', url: SERVICE_URLS.AUTH },
    { name: 'QuickBooks Service', url: SERVICE_URLS.QUICKBOOKS },
  ],
  TIMEOUT: 5000,
  INTERVAL: 30000,
};

export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
  STAGING: 'staging',
};

export const PROCESS_SIGNALS = {
  SIGTERM: 'SIGTERM',
  SIGINT: 'SIGINT',
  UNCAUGHT_EXCEPTION: 'uncaughtException',
  UNHANDLED_REJECTION: 'unhandledRejection',
};

export const AVAILABLE_ENDPOINTS = [
  API_ENDPOINTS.HEALTH,
  API_ENDPOINTS.AUTH,
  API_ENDPOINTS.QUICKBOOKS,
  API_ENDPOINTS.REPORTS,
];

export const DEFAULT_VALUES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  COMBINED: 'combined',
  DEV: 'dev',
  API_GATEWAY: 'API Gateway',
  UNKNOWN: 'Unknown',
};

export const RATE_LIMIT_CONFIG = {
  WINDOW_MINUTES: 15,
  MAX_REQUESTS: 100,
  AUTH_WINDOW_MINUTES: 15,
  AUTH_MAX_REQUESTS: 5,
  SIGNUP_WINDOW_MINUTES: 60,
  SIGNUP_MAX_REQUESTS: 3,
  API_WINDOW_MINUTES: 15,
  API_MAX_REQUESTS: 1000,
  FILE_UPLOAD_WINDOW_MINUTES: 60,
  FILE_UPLOAD_MAX_REQUESTS: 50,
};



export default {
  SERVER_CONFIG,
  SERVICE_URLS,
  API_ENDPOINTS,
  CORS_CONFIG,
  SESSION_CONFIG,
  REQUEST_CONFIG,
  HEALTH_CHECK_CONFIG,
  ENV_TYPES,
  PROCESS_SIGNALS,
  AVAILABLE_ENDPOINTS,
  DEFAULT_VALUES,
  RATE_LIMIT_CONFIG,
};
