export const ERROR_CLASS_NAMES = {
  VALIDATION_ERROR: 'ValidationError',
  AUTHENTICATION_ERROR: 'AuthenticationError',
  AUTHORIZATION_ERROR: 'AuthorizationError',
  NOT_FOUND_ERROR: 'NotFoundError',
  DUPLICATE_ERROR: 'DuplicateError',
  FOREIGN_KEY_ERROR: 'ForeignKeyError',
  DATABASE_ERROR: 'DatabaseError',
  INTERNAL_SERVER_ERROR: 'InternalServerError',
  UNAUTHORIZED_ERROR: 'UnauthorizedError',
  FORBIDDEN_ERROR: 'ForbiddenError',
  STORAGE_ERROR: 'StorageError',
  BAD_REQUEST_ERROR: 'BadRequestError',
  SEQUELIZE_VALIDATION_ERROR: 'SequelizeValidationError',
  SEQUELIZE_UNIQUE_CONSTRAINT_ERROR: 'SequelizeUniqueConstraintError',
  SEQUELIZE_FOREIGN_KEY_CONSTRAINT_ERROR: 'SequelizeForeignKeyConstraintError',
  SEQUELIZE_DATABASE_ERROR: 'SequelizeDatabaseError',
  SEQUELIZE_CONNECTION_ERROR: 'SequelizeConnectionError',
  UNKNOWN_MODULE: 'UnknownModule',
};

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  DUPLICATE_ERROR: 'DUPLICATE_ERROR',
  FOREIGN_KEY_ERROR: 'FOREIGN_KEY_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  BAD_REQUEST_ERROR: 'BAD_REQUEST_ERROR',
  EADDRINUSE: 'EADDRINUSE',
};

export const ERROR_DEFAULT_MESSAGES = {
  VALIDATION_FAILED: 'Validation failed',
  UNAUTHORIZED_ACCESS: 'Unauthorized access',
  FORBIDDEN_ACCESS: 'Forbidden access',
  RESOURCE: 'Resource',
  FIELD: 'field',
  DATABASE_OPERATION: 'database operation',
  RESOURCE_ALREADY_EXISTS: 'Resource already exists',
  INVALID_FOREIGN_KEY: 'Invalid foreign key reference',
  DATA_VALIDATION_FAILED: 'Data validation failed',
  DUPLICATE_ENTRY_DETECTED: 'Duplicate entry detected',
  REFERENCE_CONSTRAINT_VIOLATION: 'Reference constraint violation',
  DATABASE_CONNECTION_FAILED: 'Database connection failed',
  INTERNAL_SERVER_ERROR: 'Internal server error',
  UNKNOWN_ROUTE: 'Unknown route',
  API_RESOURCE: 'API resource',
  OPERATION_SUCCESSFUL: 'Operation completed successfully',
};


export default {
  ERROR_CLASS_NAMES,
  ERROR_CODES,
  ERROR_DEFAULT_MESSAGES,
};
