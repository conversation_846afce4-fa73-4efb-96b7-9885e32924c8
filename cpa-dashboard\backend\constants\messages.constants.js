export const SERVER_MESSAGES = {
  STARTING: 'Starting CPA Dashboard API Gateway...',
  ENVIRONMENT: 'Environment: {environment}',
  GATEWAY_URL: 'Gateway URL: {url}',
  MICROSERVICE_URLS: 'Microservice URLs:',
  AUTH_SERVICE: 'Auth Service',
  USERS_SERVICE: 'Users Service',
  TENANT_SERVICE: 'Tenant Service',
  QUICKBOOKS_SERVICE: 'QuickBooks Service',
  RUNNING_ON_PORT: 'Server running on port {port}',
  ACCESS_URL: 'Access URL: {url}',
  HEALTH_CHECK: 'Health check: {url}',
  PROXIED_ENDPOINTS: 'Proxied endpoints:',
  AUTHENTICATION: 'Authentication',
  USERS_ROLES: 'Users & Roles',
  TENANTS: 'Tenants',
  QUICKBOOKS: 'QuickBooks',
  REPORTS: 'Reports',
  READY: 'API Gateway is ready!',
  HEALTHY: 'healthy',
  UNHEALTHY: 'unhealthy',
  UNREACHABLE: 'unreachable',
  ALL_SERVICES_HEALTHY: 'All services are healthy',
  SOME_SERVICES_UNHEALTHY: 'Some services are unhealthy',
  GATEWAY_HEALTH_CHECK_FAILED: 'Gateway health check failed',
  CORS_NOT_ALLOWED: 'CORS policy violation',
  CORS_POLICY_VIOLATION: 'CORS policy violation',
  ORIGIN_NOT_ALLOWED: 'Origin not allowed',
  ROUTE_NOT_FOUND: 'Route not found',
  SERVICE_TEMPORARILY_UNAVAILABLE: 'service is temporarily unavailable',
  GATEWAY_ERROR: 'Gateway error: {error}',
  GATEWAY_STARTUP_FAILED: 'Gateway startup failed: {error}',
  PORT_ALREADY_IN_USE: 'Port {port} is already in use',
  RECEIVED_SIGNAL: 'Received signal: {signal}',
  SHUTDOWN_COMPLETED: 'Graceful shutdown completed',
  ERROR_DURING_SHUTDOWN: 'Error during shutdown: {error}',
  UNCAUGHT_EXCEPTION: 'Uncaught exception: {error}',
  UNHANDLED_REJECTION: 'Unhandled rejection: {reason}',
};

export const SERVICE_RESPONSE_MESSAGES = {
  SUCCESS: 'Operation completed successfully',
  INTERNAL_SERVER_ERROR: 'Internal server error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  BAD_REQUEST: 'Bad request',
  UNAUTHORIZED: 'Unauthorized',
  FORBIDDEN: 'Forbidden',
  NOT_FOUND: 'Not found',
  CONFLICT: 'Conflict',
  VALIDATION_ERROR: 'Validation error',
};

export const RATE_LIMIT_MESSAGES = {
  GENERAL_ERROR: 'Too many requests from this IP, please try again later.',
  TOO_MANY_ATTEMPTS: 'Too many attempts, please try again later.',
  AUTH_ERROR: 'Too many authentication attempts, please try again later.',
  API_LIMIT_EXCEEDED: 'API rate limit exceeded, please try again later.',
  SIGNUP_ERROR: 'Too many signup attempts, please try again later.',
  FILE_UPLOAD_ERROR: 'Too many file uploads, please try again later.',
};

export const LOG_MESSAGES = {
  SERVER_STARTING: 'Server starting...',
  SERVER_STARTED: 'Server started successfully',
  SERVER_STOPPING: 'Server stopping...',
  SERVER_STOPPED: 'Server stopped',
  SERVER_ERROR: 'Server error occurred',
  DATABASE_CONNECTING: 'Connecting to database...',
  DATABASE_CONNECTED: 'Database connected successfully',
  DATABASE_DISCONNECTING: 'Disconnecting from database...',
  DATABASE_DISCONNECTED: 'Database disconnected',
  DATABASE_ERROR: 'Database error occurred',
  AUTH_SUCCESS: 'Authentication successful',
  AUTH_FAILED: 'Authentication failed',
  AUTH_TOKEN_EXPIRED: 'Authentication token expired',
  AUTH_TOKEN_INVALID: 'Authentication token invalid',
  REQUEST_RECEIVED: 'Request received',
  REQUEST_PROCESSED: 'Request processed successfully',
  REQUEST_ERROR: 'Request processing error',
  ERROR_OCCURRED: 'Error occurred',
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_ERROR: 'Internal server error',
  EXTERNAL_ERROR: 'External service error',
  HEALTH_CHECK_FAILED: 'Health check failed',
  SERVICE_PROXY_ERROR: 'Service proxy error',
  ERROR: 'Error',
};

export const RATE_LIMIT_LOG = {
  LIMIT_EXCEEDED: (ip) => `Rate limit exceeded for IP: ${ip}`,
  STRICT_LIMIT_EXCEEDED: (ip) => `Strict rate limit exceeded for IP: ${ip}`,
  AUTH_LIMIT_EXCEEDED: (ip) => `Auth rate limit exceeded for IP: ${ip}`,
  API_LIMIT_EXCEEDED: (ip) => `API rate limit exceeded for IP: ${ip}`,
  SIGNUP_LIMIT_EXCEEDED: (ip) => `Signup rate limit exceeded for IP: ${ip}`,
  FILE_UPLOAD_LIMIT_EXCEEDED: (ip) => `File upload rate limit exceeded for IP: ${ip}`,
};

export const LOGGER_NAMES = {
  ENV_VALIDATOR: 'ENV_VALIDATOR',
};

export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  HTTP: 'http',
  VERBOSE: 'verbose',
  DEBUG: 'debug',
  SILLY: 'silly',
};

export const LOG_FORMATS = {
  COMBINED: 'combined',
  COMMON: 'common',
  DEV: 'dev',
  SHORT: 'short',
  TINY: 'tiny',
  JSON: 'json',
};



export const ERROR_MESSAGES = {
  GENERAL: 'An error occurred',
  INTERNAL_SERVER_ERROR: 'Internal server error',
};


export const VALIDATION_MESSAGES = {
  VALIDATION_FAILED: 'Validation failed',
  VALIDATION_PROCESSING_ERROR: 'Error processing validation',
  VALIDATION_FAILED_FOR_SOURCE: 'Validation failed for {source}',
  DATA_SANITIZATION_ERROR: 'Data sanitization error',
  REQUIRED_FIELD: '{field} is required',
  INVALID_FORMAT: '{field} format is invalid',
  INVALID_VALUE: '{field} value is invalid',
  MIN_LENGTH: '{field} must be at least {min} characters',
  MAX_LENGTH: '{field} must be no more than {max} characters',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_PHONE: 'Invalid phone number format',
  INVALID_URL: 'Invalid URL format',
  INVALID_DATE: 'Invalid date format',
  INVALID_NUMBER: 'Invalid number format',
  INVALID_BOOLEAN: 'Invalid boolean value',
  INVALID_ARRAY: 'Invalid array format',
  INVALID_OBJECT: 'Invalid object format',
  UNIQUE_CONSTRAINT: '{field} must be unique',
  FOREIGN_KEY_CONSTRAINT: 'Invalid reference for {field}',
  CUSTOM_VALIDATION: 'Custom validation failed for {field}',
};

export const VALIDATION_DEFAULTS = {
  DEFAULT_SOURCE: 'body',
  JOIN_SEPARATOR: '.',
  QUOTE_REPLACEMENT: "'",
  USER_AGENT_HEADER: 'User-Agent',
  DEVELOPMENT_MODE: 'development',
  MAX_ERRORS: 10,
  SANITIZE_HTML: true,
  STRIP_HTML: false,
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
  ALLOWED_ATTRIBUTES: {},
};


export const RESPONSE_MESSAGES = {
  UNKNOWN_ERROR: 'Unknown error',
};

export default {
  SERVER_MESSAGES,
  SERVICE_RESPONSE_MESSAGES,
  RATE_LIMIT_MESSAGES,
  LOG_MESSAGES,
  RATE_LIMIT_LOG,
  LOGGER_NAMES,
  LOG_LEVELS,
  LOG_FORMATS,
  ERROR_MESSAGES,
  VALIDATION_MESSAGES,
  VALIDATION_DEFAULTS,
  RESPONSE_MESSAGES,
};
