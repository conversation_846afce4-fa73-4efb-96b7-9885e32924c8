
export const LOGGER_NAMES = {
  ENV_VALIDATOR: 'ENV_VALIDATOR',
};

export const ENVIRONMENT_VARIABLES = {
  PORT: 'PORT',
  NODE_ENV: 'NODE_ENV',
  SERVER_URL: 'SERVER_URL',
  SESSION_SECRET: 'SESSION_SECRET',
  LOG_LEVEL: 'LOG_LEVEL',
  FRONTEND_URL: 'FRONTEND_URL',
  NEXT_PUBLIC_API_URL: 'NEXT_PUBLIC_API_URL',
  PRODUCTION_DOMAIN_HTTPS: 'PRODUCTION_DOMAIN_HTTPS',
  PRODUCTION_DOMAIN_HTTP: 'PRODUCTION_DOMAIN_HTTP',
  AUTH_SERVICE_URL: 'AUTH_SERVICE_URL',
  USERS_SERVICE_URL: 'USERS_SERVICE_URL',
  TENANT_SERVICE_URL: 'TENANT_SERVICE_URL',
  QUICKBOOKS_SERVICE_URL: 'QUICKBOOKS_SERVICE_URL',
  // Database Configuration
  DB_HOST: 'DB_HOST',
  DB_PORT: 'DB_PORT',
  DB_NAME: 'DB_NAME',
  DB_USER: 'DB_USER',
  DB_PASSWORD: 'DB_PASSWORD',
  DB_DIALECT: 'DB_DIALECT',
  // Local Database (for development) - same as main DB
  USE_LOCAL_DB: 'USE_LOCAL_DB',
};


export const MORGAN_FORMATS = {
  DEV: 'dev',
  PROD: 'prod',
  DETAILED: 'detailed',
  COMBINED: 'combined',
  COMMON: 'common',
  SHORT: 'short',
  TINY: 'tiny',
};

export const VALIDATION_TYPES = {
  VALIDATION_ERROR: 'validation_error',
};

export const ENCRYPTION_STRINGS = {
  UTF8: 'utf8',
  HEX: 'hex',
  BASE64URL: 'base64url',
  SHA256: 'sha256',
  TRUE: 'true',
};

export const DATABASE_STRINGS = {
  POSTGRES: 'postgres',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  HEALTHY: 'healthy',
  UNHEALTHY: 'unhealthy',
  ERROR: 'error',
  LOCAL: 'local',
  CLOUD: 'cloud',
  UNKNOWN: 'unknown',
};



export const ENVIRONMENT_ERROR_MESSAGES = {
  REQUIRED_VARIABLE_NOT_SET: (varName) => `Required environment variable ${varName} is not set`,
  MUST_BE_VALID_NUMBER: (varName) => `Environment variable ${varName} must be a valid number`,
  MUST_BE_VALID_BOOLEAN: (varName) => `Environment variable ${varName} must be a valid boolean`,
  MUST_BE_VALID_STRING: (varName) => `Environment variable ${varName} must be a valid string`,
  INVALID_FORMAT: (varName, format) => `Environment variable ${varName} has invalid format. Expected: ${format}`,
  MISSING_REQUIRED_VARS: (vars) => `Missing required environment variables: ${vars.join(', ')}`,
  CONFIGURATION_FAILED: 'Environment configuration failed',
  VALIDATION_FAILED: 'Environment variable validation failed',
};


export default {
  LOGGER_NAMES,
  ENVIRONMENT_VARIABLES,
  MORGAN_FORMATS,
  VALIDATION_TYPES,
  ENCRYPTION_STRINGS,
  DATABASE_STRINGS,
  ENVIRONMENT_ERROR_MESSAGES,
};
