import "dotenv/config";
import express from "express";
import cors from "cors";
import { ENVIRONMENT_VARIABLES } from "./constants/strings.constants.js";
import cookieParser from "cookie-parser";
import session from "express-session";
import { createProxyMiddleware } from "http-proxy-middleware";
import fetch from "node-fetch";
import { createLogger } from "./utils/logger.utils.js";
import morganMiddleware from "./middleware/morgan.middleware.js";
import { generalLimiter } from "./middleware/ratelimit.middleware.js";
import {
  validateCoreEnvVars,
  getRequiredEnvVar,
  getRequiredEnvNumber,
} from "./utils/env-validator.utils.js";
import {
  SERVER_CONFIG,
  SERVICE_URLS,
  CORS_CONFIG,
  SESSION_CONFIG,
  REQUEST_CONFIG,
  HEALTH_CHECK_CONFIG,
  API_ENDPOINTS,
  AVAILABLE_ENDPOINTS,
  PROCESS_SIGNALS,
  DEFAULT_VALUES,
} from "./constants/app.constants.js";
import {
  SERVER_MESSAGES,
  SERVICE_RESPONSE_MESSAGES,
  LOG_MESSAGES,
  RESPONSE_MESSAGES,
} from "./constants/messages.constants.js";
import { spawn } from "child_process";
import path from "path";
import { fileURLToPath } from "url";

// Validate all required environment variables
validateCoreEnvVars();

const PORT = getRequiredEnvNumber(ENVIRONMENT_VARIABLES.PORT);
const NODE_ENV = getRequiredEnvVar(ENVIRONMENT_VARIABLES.NODE_ENV);
const SERVER_URL = getRequiredEnvVar(ENVIRONMENT_VARIABLES.SERVER_URL);

// Service ports
const AUTH_PORT = process.env.AUTH_SERVICE_PORT || 3001;
const QUICKBOOKS_PORT = process.env.QUICKBOOKS_SERVICE_PORT || 3002;
const SIKKA_PORT = process.env.SIKKA_SERVICE_PORT || 3003;
const USERS_PORT = process.env.USERS_SERVICE_PORT || 3004;

// Service URLs
const AUTH_URL = process.env.AUTH_SERVICE_URL || `http://localhost:${AUTH_PORT}`;
const QUICKBOOKS_URL = process.env.QUICKBOOKS_SERVICE_URL || `http://localhost:${QUICKBOOKS_PORT}`;
const SIKKA_URL = process.env.SIKKA_SERVICE_URL || `http://localhost:${SIKKA_PORT}`;
const USERS_URL = process.env.USERS_SERVICE_URL || `http://localhost:${USERS_PORT}`;

// Create logger for API Gateway
const logger = createLogger("API_GATEWAY");

// Microservices processes
const microservices = {
  auth: null,
  quickbooks: null,
  sikka: null,
  users: null
};

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Start a microservice
 * @param {string} serviceName - Name of the service
 * @param {string} servicePath - Path to the service directory
 * @param {number} port - Port for the service
 * @returns {Promise<Object>} Process object
 */
const startMicroservice = (serviceName, servicePath, port) => {
  return new Promise((resolve, reject) => {
    const serviceDir = path.join(__dirname, servicePath);
    const packageJsonPath = path.join(serviceDir, 'package.json');
    
    // Check if package.json exists
    try {
      require(packageJsonPath);
    } catch (error) {
      console.log(`⚠️  ${serviceName} service not found at ${serviceDir}`);
      resolve(null);
      return;
    }

    console.log(`🚀 Starting ${serviceName} service on port ${port}...`);
    
    // Use cross-platform npm command
    const npmCmd = process.platform === 'win32' ? 'npm.cmd' : 'npm';
    
    const child = spawn(npmCmd, ['run', 'dev'], {
      cwd: serviceDir,
      env: { 
        ...process.env, 
        PORT: port.toString(),
        NODE_ENV: NODE_ENV
      },
      stdio: 'pipe',
      shell: true
    });

    child.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('listening') || output.includes('started') || output.includes('running')) {
        logger.info(`${serviceName} service started successfully on port ${port}`);
        resolve(child);
      }
    });

    child.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('Error') || error.includes('error')) {
        logger.error(`${serviceName} service error: ${error}`);
      }
    });

    child.on('error', (error) => {
      logger.error(`Failed to start ${serviceName} service: ${error.message}`);
      reject(error);
    });

    child.on('exit', (code) => {
      if (code !== 0) {
        logger.error(`${serviceName} service exited with code ${code}`);
      }
    });

    // Store the process
    microservices[serviceName] = child;
  });
};

/**
 * Start all microservices
 * @returns {Promise<void>}
 */
const startAllMicroservices = async () => {
  try {
    console.log('🚀 Starting all microservices...');
    
    const services = [
      { name: 'auth', path: 'api/v1/auth', port: AUTH_PORT },
      { name: 'quickbooks', path: 'api/v1/quickbook', port: QUICKBOOKS_PORT },
      { name: 'sikka', path: 'api/v1/sikka', port: SIKKA_PORT },
      { name: 'users', path: 'api/v1/users', port: USERS_PORT }
    ];

    // Start services in parallel
    const startPromises = services.map(service => 
      startMicroservice(service.name, service.path, service.port)
    );

    await Promise.allSettled(startPromises);
    
    // Wait a bit for services to fully start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('✅ All microservices startup completed');
  } catch (error) {
    console.error(`❌ Error starting microservices: ${error.message}`);
  }
};

/**
 * Stop all microservices
 */
const stopAllMicroservices = () => {
  logger.info('Stopping all microservices...');
  
  Object.entries(microservices).forEach(([name, process]) => {
    if (process) {
      logger.info(`Stopping ${name} service...`);
      process.kill('SIGTERM');
    }
  });
};

const app = express();

const corsOptions = {
  origin: (origin, callback) => {
    if (!origin) return callback(null, true);

    const isAllowedOrigin = CORS_CONFIG.ALLOWED_DOMAINS.includes(origin);
    const isAllowedDomain = origin.includes(CORS_CONFIG.ALLOWED_DOMAIN_PATTERN);

    if (isAllowedOrigin || isAllowedDomain) {
      callback(null, true);
    } else {
      callback(new Error(SERVER_MESSAGES.CORS_NOT_ALLOWED));
    }
  },
  methods: CORS_CONFIG.METHODS,
  allowedHeaders: CORS_CONFIG.ALLOWED_HEADERS,
  exposedHeaders: CORS_CONFIG.EXPOSED_HEADERS,
  credentials: CORS_CONFIG.CREDENTIALS,
  maxAge: CORS_CONFIG.MAX_AGE,
};

const sessionOptions = {
  secret: getRequiredEnvVar(ENVIRONMENT_VARIABLES.SESSION_SECRET),
  resave: false,
  saveUninitialized: false,
  name: SESSION_CONFIG.NAME,
  cookie: {
    secure: SESSION_CONFIG.SECURE,
    httpOnly: SESSION_CONFIG.HTTP_ONLY,
    sameSite: SESSION_CONFIG.SAME_SITE,
    maxAge: SESSION_CONFIG.MAX_AGE,
  },
};

app.use(generalLimiter);
app.use(cors(corsOptions));
app.use(express.json({ limit: REQUEST_CONFIG.JSON_LIMIT }));
app.use(
  express.urlencoded({
    extended: true,
    limit: REQUEST_CONFIG.URL_ENCODED_LIMIT,
  })
);
app.use(cookieParser());
app.use(session(sessionOptions));
app.use(
  morganMiddleware(
    NODE_ENV === DEFAULT_VALUES.PRODUCTION
      ? DEFAULT_VALUES.COMBINED
      : DEFAULT_VALUES.DEV
  )
);

/**
 * Health check endpoint to verify all microservices are running
 * @route GET /health
 * @returns {Object} Health status of all services
 */
app.get(API_ENDPOINTS.HEALTH, async (req, res) => {
  try {
    const healthChecks = await Promise.allSettled(
      HEALTH_CHECK_CONFIG.SERVICES.map(async (service) => {
        try {
          const response = await fetch(
            `${service.url}${API_ENDPOINTS.HEALTH}`,
            {
              method: "GET",
              timeout: HEALTH_CHECK_CONFIG.TIMEOUT,
            }
          );
          return {
            service: service.name,
            status: response.ok
              ? SERVER_MESSAGES.HEALTHY
              : SERVER_MESSAGES.UNHEALTHY,
            url: service.url,
          };
        } catch (error) {
          return {
            service: service.name,
            status: SERVER_MESSAGES.UNREACHABLE,
            url: service.url,
            error: error.message,
          };
        }
      })
    );

    const results = healthChecks.map((result) =>
      result.status === "fulfilled"
        ? result.value
        : {
            service: DEFAULT_VALUES.UNKNOWN,
            status: SERVER_MESSAGES.ERROR,
            error: result.reason?.message
              ? result.reason.message
              : RESPONSE_MESSAGES.UNKNOWN_ERROR,
          }
    );

    const allHealthy = results.every(
      (result) => result.status === SERVER_MESSAGES.HEALTHY
    );

    res.status(allHealthy ? 200 : 503).json({
      success: allHealthy,
      message: allHealthy
        ? SERVER_MESSAGES.ALL_SERVICES_HEALTHY
        : SERVER_MESSAGES.SOME_SERVICES_UNHEALTHY,
      timestamp: new Date().toISOString(),
      environment: NODE_ENV,
      services: results,
    });
  } catch (error) {
    logger.error(LOG_MESSAGES.HEALTH_CHECK_FAILED, {
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: SERVER_MESSAGES.GATEWAY_HEALTH_CHECK_FAILED,
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

/**
 * Root endpoint providing API information
 * @route GET /
 * @returns {Object} API Gateway information and available endpoints
 */
app.get(API_ENDPOINTS.ROOT, (req, res) => {
  res.status(200).json({
    message: SERVER_CONFIG.SERVICE_NAME,
    version: SERVER_CONFIG.VERSION,
    environment: NODE_ENV,
    services: {
      auth: AUTH_URL,
      quickbooks: QUICKBOOKS_URL,
      sikka: SIKKA_URL,
      users: USERS_URL,
    },
    endpoints: {
      health: API_ENDPOINTS.HEALTH,
      auth: API_ENDPOINTS.AUTH,
      quickbooks: API_ENDPOINTS.QUICKBOOKS,
      reports: API_ENDPOINTS.REPORTS,
    },
  });
});

/**
 * Creates a service proxy middleware for microservice routing
 * @param {string} path - API path to proxy
 * @param {string} target - Target service URL
 * @param {string} serviceName - Name of the service for logging
 * @returns {Function} Express middleware function
 */
const createServiceProxy = (path, target, serviceName) => {
  return app.use(
    path,
    createProxyMiddleware({
      target,
      changeOrigin: true,
      onError: (err, req, res) => {
        logger.error(`${serviceName} ${LOG_MESSAGES.SERVICE_PROXY_ERROR}`, {
          error: err.message,
          stack: err.stack,
          service: serviceName,
          path: req.path,
        });
        res.status(503).json({
          success: false,
          message: `${serviceName} ${SERVER_MESSAGES.SERVICE_TEMPORARILY_UNAVAILABLE}`,
          service: serviceName.toLowerCase(),
        });
      },
    })
  );
};

// Create service proxies
createServiceProxy(API_ENDPOINTS.AUTH, AUTH_URL, "Auth");
createServiceProxy(API_ENDPOINTS.QUICKBOOKS, QUICKBOOKS_URL, "QuickBooks");
createServiceProxy(API_ENDPOINTS.REPORTS, QUICKBOOKS_URL, "Reports");

/**
 * CORS error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
app.use((err, req, res, next) => {
  if (err.message === SERVER_MESSAGES.CORS_NOT_ALLOWED) {
    return res.status(403).json({
      success: false,
      message: SERVER_MESSAGES.CORS_POLICY_VIOLATION,
      error: SERVER_MESSAGES.ORIGIN_NOT_ALLOWED,
    });
  }
  next(err);
});

/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} _next - Express next function (unused)
 */
app.use((err, req, res, _next) => {
  logger.error(LOG_MESSAGES.ERROR, {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
  });
  res.status(500).json({
    success: false,
    message: SERVICE_RESPONSE_MESSAGES.INTERNAL_SERVER_ERROR,
    ...(NODE_ENV === DEFAULT_VALUES.DEVELOPMENT && { error: err.message }),
  });
});

/**
 * 404 handler for unmatched routes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: SERVER_MESSAGES.ROUTE_NOT_FOUND,
    path: req.originalUrl,
    service: DEFAULT_VALUES.API_GATEWAY,
    availableEndpoints: AVAILABLE_ENDPOINTS,
  });
});

/**
 * Graceful shutdown handler
 * @param {string} signal - Process signal received
 */
const gracefulShutdown = async (signal) => {
  logger.info(SERVER_MESSAGES.RECEIVED_SIGNAL, { signal });
  try {
    // Stop all microservices first
    stopAllMicroservices();
    
    // Wait a bit for services to shut down gracefully
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    logger.info(SERVER_MESSAGES.SHUTDOWN_COMPLETED);
    process.exit(0);
  } catch (error) {
    logger.error(SERVER_MESSAGES.ERROR_DURING_SHUTDOWN, {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  }
};

/**
 * Sets up process event handlers for graceful shutdown
 */
const setupProcessHandlers = () => {
  process.on(PROCESS_SIGNALS.UNCAUGHT_EXCEPTION, (error) => {
    logger.error(`Uncaught Exception: ${error.message}`, {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  });

  process.on(PROCESS_SIGNALS.UNHANDLED_REJECTION, (reason, promise) => {
    logger.error(`Unhandled Rejection: ${reason?.message || reason}`, {
      reason: reason?.message || reason,
      promise: promise?.toString(),
    });
    process.exit(1);
  });

  process.on(PROCESS_SIGNALS.SIGTERM, () =>
    gracefulShutdown(PROCESS_SIGNALS.SIGTERM)
  );
  process.on(PROCESS_SIGNALS.SIGINT, () =>
    gracefulShutdown(PROCESS_SIGNALS.SIGINT)
  );
};

/**
 * Starts the API Gateway server
 * @returns {Object} Express server instance
 */
const startServer = async () => {
  try {
    console.log(`
🔧 ===========================================
   STARTING API GATEWAY + MICROSERVICES
===========================================
📊 Service: ${SERVER_CONFIG.SERVICE_NAME}
🌍 Environment: ${NODE_ENV}
🔌 Gateway Port: ${PORT}
🌐 Gateway URL: ${SERVER_URL}

🚀 Microservices:
   • Auth Service: ${AUTH_URL} (Port ${AUTH_PORT})
   • QuickBooks Service: ${QUICKBOOKS_URL} (Port ${QUICKBOOKS_PORT})
   • Sikka Service: ${SIKKA_URL} (Port ${SIKKA_PORT})
   • Users Service: ${USERS_URL} (Port ${USERS_PORT})
===========================================
`);

    // Note: Microservices should be started separately using npm run dev:all

    const server = app.listen(PORT, () => {
      console.log(`
🚀 ===========================================
   API GATEWAY STARTED
  ===========================================
📊 Service: ${SERVER_CONFIG.SERVICE_NAME}
🌍 Environment: ${NODE_ENV}
🔌 Gateway Port: ${PORT}
🌐 Gateway URL: http://localhost:${PORT}
❤️  Health Check: http://localhost:${PORT}/health

📋 Available Endpoints:
   • Authentication: /api/v1/auth → Auth Service
   • QuickBooks: /api/v1/quickbooks → QuickBooks Service
   • Reports: /api/v1/reports → QuickBooks Service

💡 To start microservices, run: npm run dev:all
✅ API Gateway is ready and routing requests!
===========================================
`);

      // Minimal logging for production
      console.log(`✅ API Gateway running on http://localhost:${PORT}`);
      console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
    });

    server.on("error", (error) => {
      if (error.code === "EADDRINUSE") {
        logger.error(`Port ${PORT} is already in use`, { port: PORT });
      } else {
        logger.error(`Gateway error: ${error.message}`, { error: error.message });
      }
      process.exit(1);
    });

    return server;
  } catch (error) {
    logger.error(`Gateway startup failed: ${error.message}`, {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  }
};

setupProcessHandlers();
startServer();
