import { DataTypes } from 'sequelize';

const BalanceSheetModels = (sequelize) => {
  // Balance Sheet Report Model (metadata)
  const BSReport = sequelize.define(
    'BSReport',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      report_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      report_basis: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      start_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      summarize_columns_by: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      currency: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'bs_reports',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Balance Sheet Column Model
  const BSColumn = sequelize.define(
    'BSColumn',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'bs_reports',
          key: 'id',
        },
      },
      col_title: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      col_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      col_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      parent_col_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'bs_columns',
          key: 'id',
        },
      },
      parent_col_title: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      col_order: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'bs_columns',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Balance Sheet Row Model (merged accounts + values)
  const BSRow = sequelize.define(
    'BSRow',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'bs_reports',
          key: 'id',
        },
      },
      account_name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      classification: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_sub_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      column_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'bs_columns',
          key: 'id',
        },
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      value: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'bs_rows',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Define associations
  BSReport.associate = (models) => {
    BSReport.hasMany(models.BSColumn, {
      foreignKey: 'report_id',
      as: 'columns',
    });
    BSReport.hasMany(models.BSRow, {
      foreignKey: 'report_id',
      as: 'rows',
    });
  };

  BSColumn.associate = (models) => {
    BSColumn.belongsTo(models.BSReport, {
      foreignKey: 'report_id',
      as: 'report',
    });
    BSColumn.belongsTo(models.BSColumn, {
      foreignKey: 'parent_col_id',
      as: 'parent',
    });
    BSColumn.hasMany(models.BSColumn, {
      foreignKey: 'parent_col_id',
      as: 'children',
    });
    BSColumn.hasMany(models.BSRow, {
      foreignKey: 'column_id',
      as: 'rows',
    });
  };

  BSRow.associate = (models) => {
    BSRow.belongsTo(models.BSReport, {
      foreignKey: 'report_id',
      as: 'report',
    });
    BSRow.belongsTo(models.BSColumn, {
      foreignKey: 'column_id',
      as: 'column',
    });
  };

  return {
    BSReport,
    BSColumn,
    BSRow,
  };
};

export default BalanceSheetModels;
