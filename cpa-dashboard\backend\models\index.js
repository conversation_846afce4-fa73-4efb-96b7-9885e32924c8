import sequelize from "../api/v1/auth/config/postgres.config.js";

// Import all model definitions
import AppUserModel from "./app_user.model.js";
import AppTokenModel from "./app_token.model.js";
import AuditLogModel from "./audit_log.model.js";
import CountryModel from "./country.model.js";
import CurrencyModel from "./currency.model.js";
import DataSourceModel from "./data_source.model.js";
import DataSourceConnectionModel from "./data_source_connection.model.js";
import DataSourceTransactionModel from "./data_source_transaction.model.js";
import EventTypeModel from "./event_type.model.js";
import GLAccountMasterModel from "./gl_account_master.model.js";
import PaymentModel from "./payment.model.js";
import PaymentMethodTypeModel from "./payment_method_type.model.js";
import PermissionModel from "./permission.model.js";
import RoleModel from "./role.model.js";
import RolePermissionMappingModel from "./role_permission_mapping.model.js";
import SubscriptionModel from "./subscription.model.js";
import SubscriptionPlanModel from "./subscription_plan.model.js";
import TenantModel from "./tenant.model.js";
import TrialBalanceModels from "./trial_balance.model.js";
import ProfitLossModels from "./profit_loss.model.js";
import BalanceSheetModels from "./balance_sheet.model.js";
import QuickbookAccountModel from "./quickbook_account.model.js";

// Initialize models
const models = {
  app_user: AppUserModel(sequelize),
  app_token: AppTokenModel(sequelize),
  audit_log: AuditLogModel(sequelize),
  country: CountryModel(sequelize),
  currency: CurrencyModel(sequelize),
  data_source: DataSourceModel(sequelize),
  data_source_connection: DataSourceConnectionModel(sequelize),
  data_source_transaction: DataSourceTransactionModel(sequelize),
  event_type: EventTypeModel(sequelize),
  gl_account_master: GLAccountMasterModel(sequelize),
  payment: PaymentModel(sequelize),
  payment_method_type: PaymentMethodTypeModel(sequelize),
  permission: PermissionModel(sequelize),
  role: RoleModel(sequelize),
  role_permission_mapping: RolePermissionMappingModel(sequelize),
  subscription: SubscriptionModel(sequelize),
  subscription_plan: SubscriptionPlanModel(sequelize),
  tenant: TenantModel(sequelize),
  quickbook_account: QuickbookAccountModel(sequelize),
  // Report models
  ...TrialBalanceModels(sequelize),
  ...ProfitLossModels(sequelize),
  ...BalanceSheetModels(sequelize),
};

// Set up associations
Object.keys(models).forEach((modelName) => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Export individual models for convenience
export const {
  app_user: AppUser,
  app_token: AppToken,
  audit_log: AuditLog,
  country: Country,
  currency: Currency,
  data_source: DataSource,
  data_source_connection: DataSourceConnection,
  data_source_transaction: DataSourceTransaction,
  event_type: EventType,
  gl_account_master: GLAccountMaster,
  payment: Payment,
  payment_method_type: PaymentMethodType,
  permission: Permission,
  role: Role,
  role_permission_mapping: RolePermissionMapping,
  subscription: Subscription,
  subscription_plan: SubscriptionPlan,
  tenant: Tenant,
  quickbook_account: QuickbookAccount,
  // Export report models
  TrialBalanceReport,
  TrialBalanceColumn,
  TrialBalanceRow,
  PLReport,
  PLColumn,
  PLRow,
  BSReport,
  BSColumn,
  BSRow,
} = models;

// Export all models and sequelize instance
export { sequelize };
export default models;
