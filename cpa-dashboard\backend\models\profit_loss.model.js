import { DataTypes } from 'sequelize';

const ProfitLossModels = (sequelize) => {
  // Profit & Loss Report Model (metadata)
  const PLReport = sequelize.define(
    'PLReport',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      report_name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      report_basis: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      start_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_period: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      summarize_columns_by: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      currency: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'pl_reports',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Profit & Loss Column Model
  const PLColumn = sequelize.define(
    'PLColumn',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'pl_reports',
          key: 'id',
        },
      },
      col_title: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      col_type: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      col_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      parent_col_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'pl_columns',
          key: 'id',
        },
      },
      parent_col_title: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      col_order: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'pl_columns',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Profit & Loss Row Model (merged accounts + values)
  const PLRow = sequelize.define(
    'PLRow',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      report_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'pl_reports',
          key: 'id',
        },
      },
      account_name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      account_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      category: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      s2_gr3: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      account_type: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      column_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'pl_columns',
          key: 'id',
        },
      },
      realm_id: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      value: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'pl_rows',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  // Define associations
  PLReport.associate = (models) => {
    PLReport.hasMany(models.PLColumn, {
      foreignKey: 'report_id',
      as: 'columns',
    });
    PLReport.hasMany(models.PLRow, {
      foreignKey: 'report_id',
      as: 'rows',
    });
  };

  PLColumn.associate = (models) => {
    PLColumn.belongsTo(models.PLReport, {
      foreignKey: 'report_id',
      as: 'report',
    });
    PLColumn.belongsTo(models.PLColumn, {
      foreignKey: 'parent_col_id',
      as: 'parent',
    });
    PLColumn.hasMany(models.PLColumn, {
      foreignKey: 'parent_col_id',
      as: 'children',
    });
    PLColumn.hasMany(models.PLRow, {
      foreignKey: 'column_id',
      as: 'rows',
    });
  };

  PLRow.associate = (models) => {
    PLRow.belongsTo(models.PLReport, {
      foreignKey: 'report_id',
      as: 'report',
    });
    PLRow.belongsTo(models.PLColumn, {
      foreignKey: 'column_id',
      as: 'column',
    });
  };

  return {
    PLReport,
    PLColumn,
    PLRow,
  };
};

export default ProfitLossModels;
