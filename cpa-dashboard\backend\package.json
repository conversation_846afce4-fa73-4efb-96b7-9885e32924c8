{"name": "cpa-dashboard-server", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "dev:auth": "cd api/v1/auth && npm run dev", "start:auth": "cd api/v1/auth && npm start", "dev:quickbook": "cd api/v1/quickbook && npm run dev", "start:quickbook": "cd api/v1/quickbook && npm start", "dev:sikka": "cd api/v1/sikka && npm run dev", "start:sikka": "cd api/v1/sikka && npm start", "dev:users": "cd api/v1/users && npm run dev", "start:users": "cd api/v1/users && npm start", "dev:all": "concurrently \"npm run dev:auth\" \"npm run dev:quickbook\" \"npm run dev:sikka\" \"npm run dev:users\" \"npm run dev\"", "start:all": "concurrently \"npm run start:auth\" \"npm run start:quickbook\" \"npm run start:sikka\" \"npm run start:users\" \"npm run start\"", "migrate": "npx sequelize-cli db:migrate --config config/database.cjs", "migrate:undo": "npx sequelize-cli db:migrate:undo --config config/database.cjs", "migrate:undo:all": "npx sequelize-cli db:migrate:undo:all --config config/database.cjs", "migrate:status": "npx sequelize-cli db:migrate:status --config config/database.cjs", "seed": "npx sequelize-cli db:seed:all --config config/database.cjs", "seed:undo": "npx sequelize-cli db:seed:undo --config config/database.cjs", "seed:undo:all": "npx sequelize-cli db:seed:undo:all --config config/database.cjs", "seed:roles": "npx sequelize-cli db:seed --seed roles.seeder.js --config config/database.cjs", "seed:users": "npx sequelize-cli db:seed --seed users.seeder.js --config config/database.cjs", "db:reset": "npm run migrate:undo:all && npm run migrate && npm run seed", "db:setup": "npm run migrate && npm run seed", "db:test": "node test-connection.js", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false"}, "description": "CPA Dashboard API Gateway - Central routing and orchestration for microservices", "author": "Mobio Solutions", "license": "ISC", "keywords": ["cpa", "dashboard", "api-gateway", "microservices", "nodejs", "express", "proxy", "routing"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "http-proxy-middleware": "^3.0.5", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "eslint": "^9.34.0", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.3.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "sequelize-cli": "^6.6.3", "supertest": "^7.0.0"}}