import { createLogger } from './logger.utils.js';
import { LOGGER_NAMES } from '../constants/messages.constants.js';
import { ENVIRONMENT_VARIABLES, ENCRYPTION_STRINGS, ENVIRONMENT_ERROR_MESSAGES } from '../constants/strings.constants.js';

const logger = createLogger(LOGGER_NAMES.ENV_VALIDATOR);

/**
 * Required environment variables configuration
 */
export const REQUIRED_ENV_VARS = {
  // Server Configuration
  PORT: ENVIRONMENT_VARIABLES.PORT,
  NODE_ENV: ENVIRONMENT_VARIABLES.NODE_ENV,
  SERVER_URL: ENVIRONMENT_VARIABLES.SERVER_URL,
  
  // Security
  SESSION_SECRET: ENVIRONMENT_VARIABLES.SESSION_SECRET,
  
  // CORS Configuration
  FRONTEND_URL: ENVIRONMENT_VARIABLES.FRONTEND_URL,
  NEXT_PUBLIC_API_URL: ENVIRONMENT_VARIABLES.NEXT_PUBLIC_API_URL,
  PRODUCTION_DOMAIN_HTTPS: ENVIRONMENT_VARIABLES.PRODUCTION_DOMAIN_HTTPS,
  PRODUCTION_DOMAIN_HTTP: ENVIRONMENT_VARIABLES.PRODUCTION_DOMAIN_HTTP,
};

/**
 * Validates that all required environment variables are present
 * @param {Array} requiredVars - Array of required environment variable names
 * @throws {Error} If any required environment variables are missing
 */
export const validateRequiredEnvVars = (requiredVars) => {
  const missing = [];
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }
  
  if (missing.length > 0) {
    const errorMessage = `Missing required environment variables: ${missing.join(', ')}`;
    logger.error('Environment validation failed:', { missing });
    throw new Error(errorMessage);
  }
  
  logger.info('All required environment variables are present');
};

/**
 * Validates and returns environment variable value
 * @param {string} varName - Environment variable name
 * @param {string} defaultValue - Default value if not provided
 * @returns {string} Environment variable value
 * @throws {Error} If environment variable is required but not provided
 */
export const getRequiredEnvVar = (varName, defaultValue = null) => {
  const value = process.env[varName];
  
  if (!value && defaultValue === null) {
    throw new Error(ENVIRONMENT_ERROR_MESSAGES.REQUIRED_VARIABLE_NOT_SET(varName));
  }
  
  return value || defaultValue;
};

/**
 * Validates and returns environment variable as number
 * @param {string} varName - Environment variable name
 * @param {number} defaultValue - Default value if not provided
 * @returns {number} Environment variable value as number
 * @throws {Error} If environment variable is required but not provided or invalid
 */
export const getRequiredEnvNumber = (varName, defaultValue = null) => {
  const value = process.env[varName];
  
  if (!value && defaultValue === null) {
    throw new Error(ENVIRONMENT_ERROR_MESSAGES.REQUIRED_VARIABLE_NOT_SET(varName));
  }
  
  const numValue = value ? parseInt(value, 10) : defaultValue;
  
  if (isNaN(numValue)) {
    throw new Error(ENVIRONMENT_ERROR_MESSAGES.MUST_BE_VALID_NUMBER(varName));
  }
  
  return numValue;
};

/**
 * Validates and returns environment variable as boolean
 * @param {string} varName - Environment variable name
 * @param {boolean} defaultValue - Default value if not provided
 * @returns {boolean} Environment variable value as boolean
 * @throws {Error} If environment variable is required but not provided
 */
export const getRequiredEnvBoolean = (varName, defaultValue = null) => {
  const value = process.env[varName];
  
  if (!value && defaultValue === null) {
    throw new Error(ENVIRONMENT_ERROR_MESSAGES.REQUIRED_VARIABLE_NOT_SET(varName));
  }
  
  if (!value) {
    return defaultValue;
  }
  
  return value.toLowerCase() === ENCRYPTION_STRINGS.TRUE;
};

/**
 * Validates all core environment variables for the application
 * @throws {Error} If any core environment variables are missing
 */
export const validateCoreEnvVars = () => {
  const coreVars = [
    REQUIRED_ENV_VARS.PORT,
    REQUIRED_ENV_VARS.NODE_ENV,
    REQUIRED_ENV_VARS.SERVER_URL,
    REQUIRED_ENV_VARS.SESSION_SECRET,
    REQUIRED_ENV_VARS.FRONTEND_URL,
    REQUIRED_ENV_VARS.NEXT_PUBLIC_API_URL,
    REQUIRED_ENV_VARS.PRODUCTION_DOMAIN_HTTPS,
    REQUIRED_ENV_VARS.PRODUCTION_DOMAIN_HTTP,
  ];
  
  validateRequiredEnvVars(coreVars);
};


export default {
  validateRequiredEnvVars,
  getRequiredEnvVar,
  getRequiredEnvNumber,
  getRequiredEnvBoolean,
  validateCoreEnvVars,
  REQUIRED_ENV_VARS,
};
