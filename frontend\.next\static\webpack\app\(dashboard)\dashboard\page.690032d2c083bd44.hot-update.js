"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useFileOperations.js":
/*!****************************************!*\
  !*** ./src/hooks/useFileOperations.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFileOperations: () => (/* binding */ useFileOperations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.jsx\");\n/* harmony import */ var _utils_methods_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/methods/helpers */ \"(app-pages-browser)/./src/utils/methods/helpers.js\");\n/* __next_internal_client_entry_do_not_use__ useFileOperations auto */ var _s = $RefreshSig$();\n\n\n\n/**\r\n * Custom hook for file operations (upload, download, preview)\r\n * @param {Object} options - Configuration options\r\n * @returns {Object} File operation utilities and state\r\n */ const useFileOperations = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [downloading, setDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const { addToast } = (0,_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    /**\r\n   * Download a file from URL\r\n   * @param {string} url - File URL\r\n   * @param {string} filename - Desired filename\r\n   */ const download = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[download]\": async (url, filename)=>{\n            setDownloading(true);\n            try {\n                console.log(\"Downloading file from URL:\", url);\n                const response = await fetch(url);\n                if (!response.ok) {\n                    throw new Error(\"Failed to download file: \".concat(response.statusText));\n                }\n                console.log(\"File download response:\", response);\n                // Get the blob from the response\n                const blob = await response.blob();\n                console.log(\"Blob size:\", blob.size, \"bytes\");\n                console.log(\"Blob type:\", blob.type);\n                // Create object URL from the blob\n                const downloadUrl = window.URL.createObjectURL(blob);\n                // Create download link and trigger download\n                const link = document.createElement(\"a\");\n                link.href = downloadUrl;\n                link.download = filename || \"download\";\n                link.style.display = \"none\";\n                // Add to DOM, click, and remove\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                // Clean up the object URL\n                window.URL.revokeObjectURL(downloadUrl);\n                addToast(\"File downloaded successfully\", \"success\");\n            } catch (error) {\n                console.error(\"Download error:\", error);\n                addToast(error.message || \"Failed to download file\", \"error\");\n                throw error;\n            } finally{\n                setDownloading(false);\n            }\n        }\n    }[\"useFileOperations.useCallback[download]\"], [\n        addToast\n    ]);\n    /**\r\n   * Upload files with progress tracking\r\n   * @param {FileList|File[]} files - Files to upload\r\n   * @param {Function} uploadFunction - Function to handle upload\r\n   * @param {Object} uploadOptions - Additional upload options\r\n   */ const upload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[upload]\": async function(files, uploadFunction) {\n            let uploadOptions = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n            if (!files || files.length === 0) return;\n            setUploading(true);\n            setUploadProgress(0);\n            try {\n                const fileArray = Array.from(files);\n                const results = [];\n                for(let i = 0; i < fileArray.length; i++){\n                    const file = fileArray[i];\n                    // Validate file if validation function provided\n                    if (uploadOptions.validateFile) {\n                        const validation = uploadOptions.validateFile(file);\n                        if (!validation.isValid) {\n                            throw new Error(validation.message);\n                        }\n                    }\n                    // Upload file\n                    const result = await uploadFunction(file, {\n                        ...uploadOptions,\n                        onProgress: {\n                            \"useFileOperations.useCallback[upload]\": (progress)=>{\n                                const totalProgress = (i / fileArray.length + progress / 100 / fileArray.length) * 100;\n                                setUploadProgress(totalProgress);\n                            }\n                        }[\"useFileOperations.useCallback[upload]\"]\n                    });\n                    results.push({\n                        file,\n                        result\n                    });\n                }\n                setUploadedFiles({\n                    \"useFileOperations.useCallback[upload]\": (prev)=>[\n                            ...prev,\n                            ...results\n                        ]\n                }[\"useFileOperations.useCallback[upload]\"]);\n                addToast(\"\".concat(fileArray.length, \" file(s) uploaded successfully\"), \"success\");\n                return results;\n            } catch (error) {\n                addToast(error.message || \"Failed to upload file(s)\", \"error\");\n                throw error;\n            } finally{\n                setUploading(false);\n                setUploadProgress(0);\n            }\n        }\n    }[\"useFileOperations.useCallback[upload]\"], [\n        addToast\n    ]);\n    /**\r\n   * Convert file to base64\r\n   * @param {File} file - File to convert\r\n   */ const convertToBase64 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[convertToBase64]\": async (file)=>{\n            try {\n                return await (0,_utils_methods_helpers__WEBPACK_IMPORTED_MODULE_2__.fileToBase64)(file);\n            } catch (error) {\n                addToast(\"Failed to convert file\", \"error\");\n                throw error;\n            }\n        }\n    }[\"useFileOperations.useCallback[convertToBase64]\"], [\n        addToast\n    ]);\n    /**\r\n   * Preview file (for images, PDFs, etc.)\r\n   * @param {File} file - File to preview\r\n   */ const previewFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[previewFile]\": (file)=>{\n            if (!file) return null;\n            const url = URL.createObjectURL(file);\n            // Return cleanup function\n            return {\n                url,\n                cleanup: ({\n                    \"useFileOperations.useCallback[previewFile]\": ()=>URL.revokeObjectURL(url)\n                })[\"useFileOperations.useCallback[previewFile]\"]\n            };\n        }\n    }[\"useFileOperations.useCallback[previewFile]\"], []);\n    /**\r\n   * Validate file type and size\r\n   * @param {File} file - File to validate\r\n   * @param {Object} rules - Validation rules\r\n   */ const validateFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[validateFile]\": function(file) {\n            let rules = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n            const { allowedTypes = [], maxSize = Infinity, minSize = 0 } = rules;\n            // Check file type\n            if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {\n                return {\n                    isValid: false,\n                    message: \"File type \".concat(file.type, \" is not allowed. Allowed types: \").concat(allowedTypes.join(\", \"))\n                };\n            }\n            // Check file size\n            if (file.size > maxSize) {\n                return {\n                    isValid: false,\n                    message: \"File size (\".concat((file.size / 1024 / 1024).toFixed(2), \"MB) exceeds maximum allowed size (\").concat((maxSize / 1024 / 1024).toFixed(2), \"MB)\")\n                };\n            }\n            if (file.size < minSize) {\n                return {\n                    isValid: false,\n                    message: \"File size is too small. Minimum size: \".concat((minSize / 1024).toFixed(2), \"KB\")\n                };\n            }\n            return {\n                isValid: true,\n                message: \"\"\n            };\n        }\n    }[\"useFileOperations.useCallback[validateFile]\"], []);\n    /**\r\n   * Remove uploaded file from state\r\n   * @param {number} index - Index of file to remove\r\n   */ const removeUploadedFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[removeUploadedFile]\": (index)=>{\n            setUploadedFiles({\n                \"useFileOperations.useCallback[removeUploadedFile]\": (prev)=>prev.filter({\n                        \"useFileOperations.useCallback[removeUploadedFile]\": (_, i)=>i !== index\n                    }[\"useFileOperations.useCallback[removeUploadedFile]\"])\n            }[\"useFileOperations.useCallback[removeUploadedFile]\"]);\n        }\n    }[\"useFileOperations.useCallback[removeUploadedFile]\"], []);\n    /**\r\n   * Clear all uploaded files\r\n   */ const clearUploadedFiles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[clearUploadedFiles]\": ()=>{\n            setUploadedFiles([]);\n        }\n    }[\"useFileOperations.useCallback[clearUploadedFiles]\"], []);\n    /**\r\n   * Get file extension\r\n   * @param {string} filename - Filename\r\n   */ const getFileExtension = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[getFileExtension]\": (filename)=>{\n            var _filename_split_pop;\n            return ((_filename_split_pop = filename.split('.').pop()) === null || _filename_split_pop === void 0 ? void 0 : _filename_split_pop.toLowerCase()) || '';\n        }\n    }[\"useFileOperations.useCallback[getFileExtension]\"], []);\n    /**\r\n   * Format file size\r\n   * @param {number} bytes - File size in bytes\r\n   */ const formatFileSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[formatFileSize]\": (bytes)=>{\n            if (bytes === 0) return '0 Bytes';\n            const k = 1024;\n            const sizes = [\n                'Bytes',\n                'KB',\n                'MB',\n                'GB',\n                'TB'\n            ];\n            const i = Math.floor(Math.log(bytes) / Math.log(k));\n            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n        }\n    }[\"useFileOperations.useCallback[formatFileSize]\"], []);\n    /**\r\n   * Check if file is an image\r\n   * @param {File|string} file - File object or MIME type\r\n   */ const isImage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[isImage]\": (file)=>{\n            const mimeType = typeof file === 'string' ? file : file.type;\n            return mimeType.startsWith('image/');\n        }\n    }[\"useFileOperations.useCallback[isImage]\"], []);\n    /**\r\n   * Check if file is a PDF\r\n   * @param {File|string} file - File object or MIME type\r\n   */ const isPDF = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useFileOperations.useCallback[isPDF]\": (file)=>{\n            const mimeType = typeof file === 'string' ? file : file.type;\n            return mimeType === 'application/pdf';\n        }\n    }[\"useFileOperations.useCallback[isPDF]\"], []);\n    return {\n        // State\n        uploading,\n        downloading,\n        uploadProgress,\n        uploadedFiles,\n        // Operations\n        download,\n        upload,\n        convertToBase64,\n        previewFile,\n        validateFile,\n        // File management\n        removeUploadedFile,\n        clearUploadedFiles,\n        // Utilities\n        getFileExtension,\n        formatFileSize,\n        isImage,\n        isPDF\n    };\n};\n_s(useFileOperations, \"2bkHFriIJHp9Jko5t5ux2EgQFfI=\", false, function() {\n    return [\n        _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.useToast\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useFileOperations.js\n"));

/***/ })

});